<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>DockWidget</class>
 <widget class="QDockWidget" name="DockWidget">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>483</width>
    <height>565</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>DockWidget</string>
  </property>
  <widget class="QWidget" name="dockWidgetContents">
   <widget class="QLabel" name="lb_xunlu">
    <property name="geometry">
     <rect>
      <x>0</x>
      <y>0</y>
      <width>480</width>
      <height>270</height>
     </rect>
    </property>
    <property name="minimumSize">
     <size>
      <width>0</width>
      <height>0</height>
     </size>
    </property>
    <property name="maximumSize">
     <size>
      <width>16777215</width>
      <height>16777215</height>
     </size>
    </property>
    <property name="styleSheet">
     <string notr="true">background-color: rgb(0, 0, 0);</string>
    </property>
    <property name="text">
     <string/>
    </property>
   </widget>
   <widget class="QLabel" name="lb_yolov">
    <property name="geometry">
     <rect>
      <x>0</x>
      <y>270</y>
      <width>480</width>
      <height>270</height>
     </rect>
    </property>
    <property name="minimumSize">
     <size>
      <width>480</width>
      <height>270</height>
     </size>
    </property>
    <property name="maximumSize">
     <size>
      <width>1920</width>
      <height>1080</height>
     </size>
    </property>
    <property name="styleSheet">
     <string notr="true">background-color: rgb(20, 20, 20);</string>
    </property>
    <property name="text">
     <string/>
    </property>
   </widget>
   <widget class="QPushButton" name="bt_jia">
    <property name="geometry">
     <rect>
      <x>10</x>
      <y>10</y>
      <width>21</width>
      <height>21</height>
     </rect>
    </property>
    <property name="text">
     <string>+</string>
    </property>
   </widget>
   <widget class="QPushButton" name="bt_jian">
    <property name="geometry">
     <rect>
      <x>30</x>
      <y>10</y>
      <width>21</width>
      <height>21</height>
     </rect>
    </property>
    <property name="text">
     <string>-</string>
    </property>
   </widget>
   <zorder>lb_yolov</zorder>
   <zorder>lb_xunlu</zorder>
   <zorder>bt_jia</zorder>
   <zorder>bt_jian</zorder>
  </widget>
 </widget>
 <resources/>
 <connections/>
</ui>
