# -*- coding: utf-8 -*-

# Form implementation generated from reading ui file 'formitemlianzhao.ui'
#
# Created by: PyQt5 UI code generator 5.15.7
#
# WARNING: Any manual changes made to this file will be lost when pyuic5 is
# run again.  Do not edit this file unless you know what you are doing.


from PyQt5 import Qt<PERSON>ore, QtGui, QtWidgets


class Ui_formitemlianzhao(object):
    def setupUi(self, formitemlianzhao):
        formitemlianzhao.setObjectName("formitemlianzhao")
        formitemlianzhao.resize(301, 176)
        self.gridLayout = QtWidgets.QGridLayout(formitemlianzhao)
        self.gridLayout.setObjectName("gridLayout")
        self.horizontalLayout = QtWidgets.QHBoxLayout()
        self.horizontalLayout.setObjectName("horizontalLayout")
        self.led_name = QtWidgets.QLineEdit(formitemlianzhao)
        self.led_name.setMinimumSize(QtCore.QSize(0, 0))
        self.led_name.setMaximumSize(QtCore.QSize(999, 9999))
        self.led_name.setStyleSheet("color: rgb(255, 255, 255);")
        self.led_name.setAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignLeft|QtCore.Qt.AlignVCenter)
        self.led_name.setObjectName("led_name")
        self.horizontalLayout.addWidget(self.led_name)
        self.cb_is_checked = QtWidgets.QCheckBox(formitemlianzhao)
        self.cb_is_checked.setMinimumSize(QtCore.QSize(0, 0))
        self.cb_is_checked.setMaximumSize(QtCore.QSize(999999, 9999999))
        self.cb_is_checked.setLayoutDirection(QtCore.Qt.RightToLeft)
        self.cb_is_checked.setStyleSheet("")
        self.cb_is_checked.setText("")
        self.cb_is_checked.setChecked(True)
        self.cb_is_checked.setObjectName("cb_is_checked")
        self.horizontalLayout.addWidget(self.cb_is_checked)
        self.gridLayout.addLayout(self.horizontalLayout, 0, 0, 1, 2)
        self.cbb_lianzhao = QtWidgets.QComboBox(formitemlianzhao)
        self.cbb_lianzhao.setMinimumSize(QtCore.QSize(50, 50))
        self.cbb_lianzhao.setMaximumSize(QtCore.QSize(9999, 9999))
        self.cbb_lianzhao.setStyleSheet("color: rgb(255, 255, 255);\n"
"margin: 0px; \n"
"padding: 0px;\n"
"")
        self.cbb_lianzhao.setEditable(False)
        self.cbb_lianzhao.setInsertPolicy(QtWidgets.QComboBox.NoInsert)
        self.cbb_lianzhao.setDuplicatesEnabled(False)
        self.cbb_lianzhao.setFrame(True)
        self.cbb_lianzhao.setObjectName("cbb_lianzhao")
        self.gridLayout.addWidget(self.cbb_lianzhao, 1, 0, 1, 2)
        self.bt_del = QtWidgets.QPushButton(formitemlianzhao)
        self.bt_del.setMinimumSize(QtCore.QSize(50, 50))
        self.bt_del.setMaximumSize(QtCore.QSize(999999, 999999))
        self.bt_del.setObjectName("bt_del")
        self.gridLayout.addWidget(self.bt_del, 2, 0, 1, 1)
        self.bt_start = QtWidgets.QPushButton(formitemlianzhao)
        self.bt_start.setMinimumSize(QtCore.QSize(50, 50))
        self.bt_start.setMaximumSize(QtCore.QSize(999999, 999999))
        self.bt_start.setObjectName("bt_start")
        self.gridLayout.addWidget(self.bt_start, 2, 1, 1, 1)

        self.retranslateUi(formitemlianzhao)
        QtCore.QMetaObject.connectSlotsByName(formitemlianzhao)

    def retranslateUi(self, formitemlianzhao):
        _translate = QtCore.QCoreApplication.translate
        formitemlianzhao.setWindowTitle(_translate("formitemlianzhao", "Form"))
        self.led_name.setText(_translate("formitemlianzhao", "蒙德锄地"))
        self.bt_del.setText(_translate("formitemlianzhao", "删除\n"
"任务"))
        self.bt_start.setText(_translate("formitemlianzhao", "启动"))
