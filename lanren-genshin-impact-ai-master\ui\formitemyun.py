# -*- coding: utf-8 -*-

# Form implementation generated from reading ui file 'formitemyun.ui'
#
# Created by: PyQt5 UI code generator 5.15.7
#
# WARNING: Any manual changes made to this file will be lost when pyuic5 is
# run again.  Do not edit this file unless you know what you are doing.


from PyQt5 import QtCore, QtGui, QtWidgets


class Ui_formitemyun(object):
    def setupUi(self, formitemyun):
        formitemyun.setObjectName("formitemyun")
        formitemyun.resize(251, 144)
        formitemyun.setStyleSheet("")
        self.gridLayout = QtWidgets.QGridLayout(formitemyun)
        self.gridLayout.setObjectName("gridLayout")
        self.bt_down = QtWidgets.QPushButton(formitemyun)
        self.bt_down.setMinimumSize(QtCore.QSize(0, 30))
        self.bt_down.setObjectName("bt_down")
        self.gridLayout.addWidget(self.bt_down, 4, 0, 1, 2)
        self.led_time = QtWidgets.QLineEdit(formitemyun)
        font = QtGui.QFont()
        font.setFamily("微软雅黑")
        font.setPointSize(11)
        self.led_time.setFont(font)
        self.led_time.setStyleSheet("color: rgb(255, 255, 255);")
        self.led_time.setObjectName("led_time")
        self.gridLayout.addWidget(self.led_time, 3, 1, 1, 1)
        self.label_2 = QtWidgets.QLabel(formitemyun)
        self.label_2.setObjectName("label_2")
        self.gridLayout.addWidget(self.label_2, 3, 0, 1, 1)
        self.label = QtWidgets.QLabel(formitemyun)
        self.label.setObjectName("label")
        self.gridLayout.addWidget(self.label, 2, 0, 1, 1)
        self.label_3 = QtWidgets.QLabel(formitemyun)
        self.label_3.setObjectName("label_3")
        self.gridLayout.addWidget(self.label_3, 0, 0, 1, 1)
        self.led_author = QtWidgets.QLineEdit(formitemyun)
        font = QtGui.QFont()
        font.setFamily("微软雅黑")
        font.setPointSize(11)
        self.led_author.setFont(font)
        self.led_author.setStyleSheet("color: rgb(255, 255, 255);")
        self.led_author.setObjectName("led_author")
        self.gridLayout.addWidget(self.led_author, 2, 1, 1, 1)
        self.led_name = QtWidgets.QLineEdit(formitemyun)
        font = QtGui.QFont()
        font.setFamily("微软雅黑")
        font.setPointSize(11)
        self.led_name.setFont(font)
        self.led_name.setStyleSheet("color: rgb(255, 255, 255);")
        self.led_name.setObjectName("led_name")
        self.gridLayout.addWidget(self.led_name, 0, 1, 1, 1)

        self.retranslateUi(formitemyun)
        QtCore.QMetaObject.connectSlotsByName(formitemyun)

    def retranslateUi(self, formitemyun):
        _translate = QtCore.QCoreApplication.translate
        formitemyun.setWindowTitle(_translate("formitemyun", "Form"))
        self.bt_down.setText(_translate("formitemyun", "下载文件"))
        self.label_2.setText(_translate("formitemyun", "时  间:"))
        self.label.setText(_translate("formitemyun", "分享者:"))
        self.label_3.setText(_translate("formitemyun", "文件名:"))
