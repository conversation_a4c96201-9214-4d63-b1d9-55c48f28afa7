<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>formpy</class>
 <widget class="QWidget" name="formpy">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>316</width>
    <height>196</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <layout class="QGridLayout" name="gridLayout">
   <item row="0" column="0" colspan="4">
    <layout class="QHBoxLayout" name="horizontalLayout">
     <item>
      <widget class="QLineEdit" name="led_name">
       <property name="minimumSize">
        <size>
         <width>0</width>
         <height>0</height>
        </size>
       </property>
       <property name="maximumSize">
        <size>
         <width>999</width>
         <height>9999</height>
        </size>
       </property>
       <property name="styleSheet">
        <string notr="true">color: rgb(255, 255, 255);</string>
       </property>
       <property name="text">
        <string>蒙德锄地</string>
       </property>
       <property name="alignment">
        <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QCheckBox" name="cb_is_checked">
       <property name="minimumSize">
        <size>
         <width>0</width>
         <height>0</height>
        </size>
       </property>
       <property name="maximumSize">
        <size>
         <width>999999</width>
         <height>9999999</height>
        </size>
       </property>
       <property name="layoutDirection">
        <enum>Qt::RightToLeft</enum>
       </property>
       <property name="styleSheet">
        <string notr="true"/>
       </property>
       <property name="text">
        <string/>
       </property>
       <property name="checked">
        <bool>true</bool>
       </property>
      </widget>
     </item>
    </layout>
   </item>
   <item row="1" column="1">
    <widget class="QPushButton" name="bt_arg">
     <property name="minimumSize">
      <size>
       <width>50</width>
       <height>50</height>
      </size>
     </property>
     <property name="text">
      <string>参数</string>
     </property>
    </widget>
   </item>
   <item row="1" column="2">
    <widget class="QPushButton" name="bt_opendir">
     <property name="minimumSize">
      <size>
       <width>50</width>
       <height>50</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>99999</width>
       <height>99999</height>
      </size>
     </property>
     <property name="text">
      <string>打开目录</string>
     </property>
    </widget>
   </item>
   <item row="1" column="3">
    <widget class="QPushButton" name="bt_start">
     <property name="minimumSize">
      <size>
       <width>50</width>
       <height>50</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>999999</width>
       <height>999999</height>
      </size>
     </property>
     <property name="text">
      <string>启动</string>
     </property>
    </widget>
   </item>
   <item row="2" column="0">
    <widget class="QPushButton" name="bt_chuansong">
     <property name="minimumSize">
      <size>
       <width>50</width>
       <height>50</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>999999</width>
       <height>999999</height>
      </size>
     </property>
     <property name="text">
      <string>传送
脚本</string>
     </property>
    </widget>
   </item>
   <item row="2" column="1">
    <widget class="QPushButton" name="bt_moban">
     <property name="minimumSize">
      <size>
       <width>50</width>
       <height>50</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>99999</width>
       <height>99999</height>
      </size>
     </property>
     <property name="text">
      <string>传送
模板</string>
     </property>
    </widget>
   </item>
   <item row="2" column="2">
    <widget class="QPushButton" name="bt_moban_maodian">
     <property name="minimumSize">
      <size>
       <width>50</width>
       <height>50</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>999</width>
       <height>9999</height>
      </size>
     </property>
     <property name="text">
      <string>锚点
模板</string>
     </property>
    </widget>
   </item>
   <item row="2" column="3">
    <widget class="QPushButton" name="bt_del">
     <property name="minimumSize">
      <size>
       <width>50</width>
       <height>50</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>99999</width>
       <height>99999</height>
      </size>
     </property>
     <property name="text">
      <string>删除
任务</string>
     </property>
    </widget>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
