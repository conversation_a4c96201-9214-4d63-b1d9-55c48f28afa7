import configparser
import time
import traceback  # 异常处理
import tangbaowss  # 躺宝通讯
from lanrenauto.findpic.findimg import *  # 找图 搜图
from lanrenauto.tools.screenshot import screenshot  # 截图
from lanrenauto.yolov.lanrenonnx import LanRenOnnxYolov

class LanRenPY():
    def __init__(self, name: str, arg: str, opendir: str, yolov: LanRenOnnxYolov):
        self.name = name # 任务名
        self.arg = arg # 用这个可以提前设定一些参数 比如 循环次数等
        self.opendir = opendir  # 用这个可以找到任务文件夹目录  注意: ./是项目根目录
        self.yolov = yolov # 全局的yolov对象

        if os.path.isfile(self.arg):
            # 创建 ConfigParser 对象
            config = configparser.ConfigParser()
            # 加载 INI 文件
            config.read(self.arg)

            logger.info(str(config.getint('seting', 'times', fallback=1)))
            logger.info(config.get('seting', 'arg1', fallback="arg1"))
            logger.info(config.get('seting', 'arg2', fallback="arg2"))
    def yolov_show(self):
        state.QT_信号.mysig_show_yolov.emit()
    def run(self):
        '''
        入口函数 这个函数格式不支持修改,参数也不要改 是模板函数
        :return: bool 必须return bool类型  True or False
        '''
        # 寻找游戏窗口句柄
        hwnd = win32gui.FindWindow("UnityWndClass", state.GAME_TITLE)
        left, top, right, bottom = 0, 0, 1920, 1080
        try:
            # 从这里开始写执行代码
            logger.info(f"py脚本开始执行代码了!")
            time.sleep(3)

            # -----------------全局暂停/停止-------------------------
            while True:

                if state.状态_循环开关 == False:
                    state.状态_是否回放中 = False
                    state.状态_检测中 = False
                    state.状态_需重新传送 = False
                    tangbaowss.send_msg("是否回放#@@#假")
                    return False, "0"
                if  not state.状态_全局暂停:
                    break
                else:
                    tangbaowss.send_msg("是否回放#@@#假")
                    time.sleep(2)

            # ----------------------------------------------------

            # ---------------------激活游戏窗口-------------------------------
            if get_window_handle_at_mouse_position() != hwnd:
                tangbaowss.send_msg("是否回放#@@#假")
                time.sleep(0.1)
                # 激活hwnd
                hwnd = win32gui.FindWindow("UnityWndClass", state.GAME_TITLE)
                set_window_activate(hwnd)
                time.sleep(0.2)
            # yolov目标检测案例
            img_big = screenshot(hwnd, left, top, right, bottom, filename=None)
            datas, state.图片_YOLOV = self.yolov.detect(img_big, plot_box=state.开关_是否展预测结果)
            #展示预测结果
            if state.开关_是否展预测结果: self.yolov_show();
            logger.info(str(datas))
            # 识图点击 案例
            img_small = cv2.imdecode(np.fromfile(file="./datas/派蒙.png", dtype=np.uint8), cv2.IMREAD_UNCHANGED)
            find_img_and_click(img_small, hwnd, left, top, right, bottom, label="点击 派蒙",is_click=True,is_show=True)
            # 完成后返回结果
            state.状态_需重新传送 = False
            logger.info(f"{self.name} py执行完毕!")

            #返回时可以指定 下一个任务 从1 开始 值超过总数 则为这 整个任务包 结束了
            #支持 +xxx  -xxx  比如 +1 -1
            #"0"或者不提供 则为不干扰顺序
            return True,"0"
        except:
            state.状态_需重新传送 = False
            logger.error(f"{self.name} py执行错误: {traceback.format_exc()}")
            return False,"0"
