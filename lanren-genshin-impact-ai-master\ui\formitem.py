# -*- coding: utf-8 -*-

# Form implementation generated from reading ui file 'formitem.ui'
#
# Created by: PyQt5 UI code generator 5.15.7
#
# WARNING: Any manual changes made to this file will be lost when pyuic5 is
# run again.  Do not edit this file unless you know what you are doing.


from PyQt5 import Qt<PERSON>ore, QtGui, QtWidgets


class Ui_formitem(object):
    def setupUi(self, formitem):
        formitem.setObjectName("formitem")
        formitem.resize(348, 182)
        self.gridLayout = QtWidgets.QGridLayout(formitem)
        self.gridLayout.setObjectName("gridLayout")
        self.horizontalLayout = QtWidgets.QHBoxLayout()
        self.horizontalLayout.setObjectName("horizontalLayout")
        self.led_name = QtWidgets.QLineEdit(formitem)
        self.led_name.setMinimumSize(QtCore.QSize(0, 0))
        self.led_name.setMaximumSize(QtCore.QSize(999, 9999))
        self.led_name.setStyleSheet("color: rgb(255, 255, 255);")
        self.led_name.setAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignLeft|QtCore.Qt.AlignVCenter)
        self.led_name.setObjectName("led_name")
        self.horizontalLayout.addWidget(self.led_name)
        self.cb_is_checked = QtWidgets.QCheckBox(formitem)
        self.cb_is_checked.setMinimumSize(QtCore.QSize(0, 0))
        self.cb_is_checked.setMaximumSize(QtCore.QSize(999999, 9999999))
        self.cb_is_checked.setLayoutDirection(QtCore.Qt.RightToLeft)
        self.cb_is_checked.setStyleSheet("")
        self.cb_is_checked.setText("")
        self.cb_is_checked.setChecked(True)
        self.cb_is_checked.setObjectName("cb_is_checked")
        self.horizontalLayout.addWidget(self.cb_is_checked)
        self.gridLayout.addLayout(self.horizontalLayout, 0, 0, 1, 4)
        self.verticalLayout = QtWidgets.QVBoxLayout()
        self.verticalLayout.setSpacing(1)
        self.verticalLayout.setObjectName("verticalLayout")
        self.cb_wakuang = QtWidgets.QCheckBox(formitem)
        self.cb_wakuang.setToolTipDuration(0)
        self.cb_wakuang.setChecked(True)
        self.cb_wakuang.setObjectName("cb_wakuang")
        self.verticalLayout.addWidget(self.cb_wakuang)
        self.cb_caiji = QtWidgets.QCheckBox(formitem)
        self.cb_caiji.setChecked(False)
        self.cb_caiji.setObjectName("cb_caiji")
        self.verticalLayout.addWidget(self.cb_caiji)
        self.cb_daguai = QtWidgets.QCheckBox(formitem)
        self.cb_daguai.setChecked(True)
        self.cb_daguai.setObjectName("cb_daguai")
        self.verticalLayout.addWidget(self.cb_daguai)
        self.gridLayout.addLayout(self.verticalLayout, 1, 0, 1, 1)
        self.bt_map = QtWidgets.QPushButton(formitem)
        self.bt_map.setMinimumSize(QtCore.QSize(50, 50))
        self.bt_map.setMaximumSize(QtCore.QSize(999999, 999999))
        self.bt_map.setObjectName("bt_map")
        self.gridLayout.addWidget(self.bt_map, 1, 1, 1, 1)
        self.bt_lujing = QtWidgets.QPushButton(formitem)
        self.bt_lujing.setMinimumSize(QtCore.QSize(50, 50))
        self.bt_lujing.setMaximumSize(QtCore.QSize(99999, 99999))
        self.bt_lujing.setObjectName("bt_lujing")
        self.gridLayout.addWidget(self.bt_lujing, 1, 2, 1, 1)
        self.bt_start = QtWidgets.QPushButton(formitem)
        self.bt_start.setMinimumSize(QtCore.QSize(50, 50))
        self.bt_start.setMaximumSize(QtCore.QSize(999999, 999999))
        self.bt_start.setObjectName("bt_start")
        self.gridLayout.addWidget(self.bt_start, 1, 3, 1, 1)
        self.bt_chuansong = QtWidgets.QPushButton(formitem)
        self.bt_chuansong.setMinimumSize(QtCore.QSize(50, 50))
        self.bt_chuansong.setMaximumSize(QtCore.QSize(999999, 999999))
        self.bt_chuansong.setObjectName("bt_chuansong")
        self.gridLayout.addWidget(self.bt_chuansong, 2, 0, 1, 1)
        self.bt_moban = QtWidgets.QPushButton(formitem)
        self.bt_moban.setMinimumSize(QtCore.QSize(50, 50))
        self.bt_moban.setMaximumSize(QtCore.QSize(99999, 99999))
        self.bt_moban.setObjectName("bt_moban")
        self.gridLayout.addWidget(self.bt_moban, 2, 1, 1, 1)
        self.bt_moban_maodian = QtWidgets.QPushButton(formitem)
        self.bt_moban_maodian.setMinimumSize(QtCore.QSize(50, 50))
        self.bt_moban_maodian.setMaximumSize(QtCore.QSize(999, 9999))
        self.bt_moban_maodian.setObjectName("bt_moban_maodian")
        self.gridLayout.addWidget(self.bt_moban_maodian, 2, 2, 1, 1)
        self.bt_del = QtWidgets.QPushButton(formitem)
        self.bt_del.setMinimumSize(QtCore.QSize(50, 50))
        self.bt_del.setMaximumSize(QtCore.QSize(99999, 99999))
        self.bt_del.setObjectName("bt_del")
        self.gridLayout.addWidget(self.bt_del, 2, 3, 1, 1)

        self.retranslateUi(formitem)
        QtCore.QMetaObject.connectSlotsByName(formitem)

    def retranslateUi(self, formitem):
        _translate = QtCore.QCoreApplication.translate
        formitem.setWindowTitle(_translate("formitem", "Form"))
        self.led_name.setText(_translate("formitem", "蒙德锄地"))
        self.cb_wakuang.setText(_translate("formitem", "矿"))
        self.cb_caiji.setText(_translate("formitem", "花"))
        self.cb_daguai.setText(_translate("formitem", "怪"))
        self.bt_map.setText(_translate("formitem", "地图"))
        self.bt_lujing.setText(_translate("formitem", "路径"))
        self.bt_start.setText(_translate("formitem", "启动"))
        self.bt_chuansong.setText(_translate("formitem", "传送\n"
"脚本"))
        self.bt_moban.setText(_translate("formitem", "传送\n"
"模板"))
        self.bt_moban_maodian.setText(_translate("formitem", "锚点\n"
"模板"))
        self.bt_del.setText(_translate("formitem", "删除\n"
"任务"))
