<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>dialogchuansong</class>
 <widget class="QDialog" name="dialogchuansong">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>408</width>
    <height>156</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>设置传送脚本</string>
  </property>
  <layout class="QGridLayout" name="gridLayout">
   <item row="0" column="0">
    <widget class="QLabel" name="label_5">
     <property name="text">
      <string>选择区域:</string>
     </property>
    </widget>
   </item>
   <item row="0" column="1" colspan="2">
    <widget class="QComboBox" name="cbb_address">
     <property name="minimumSize">
      <size>
       <width>30</width>
       <height>30</height>
      </size>
     </property>
     <property name="styleSheet">
      <string notr="true">color: rgb(255, 255, 255);
margin: 0px; 
padding: 0px;</string>
     </property>
     <item>
      <property name="text">
       <string>手动录制</string>
      </property>
     </item>
    </widget>
   </item>
   <item row="2" column="2">
    <widget class="QPushButton" name="bt_huifang">
     <property name="minimumSize">
      <size>
       <width>50</width>
       <height>50</height>
      </size>
     </property>
     <property name="text">
      <string>回放</string>
     </property>
    </widget>
   </item>
   <item row="2" column="0">
    <widget class="QPushButton" name="bt_luzhi">
     <property name="minimumSize">
      <size>
       <width>50</width>
       <height>50</height>
      </size>
     </property>
     <property name="text">
      <string>录制</string>
     </property>
    </widget>
   </item>
   <item row="1" column="0" colspan="2">
    <widget class="QLabel" name="label">
     <property name="text">
      <string>请选择一个配置方式:</string>
     </property>
    </widget>
   </item>
   <item row="2" column="1">
    <widget class="QPushButton" name="bt_wenjianxuan">
     <property name="minimumSize">
      <size>
       <width>50</width>
       <height>50</height>
      </size>
     </property>
     <property name="text">
      <string>文件中选择</string>
     </property>
    </widget>
   </item>
   <item row="1" column="2">
    <widget class="QCommandLinkButton" name="clb_jiaocheng">
     <property name="text">
      <string>教程</string>
     </property>
    </widget>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
