# -*- coding: utf-8 -*-

import random
import threading
import traceback
import tangbaowss
from lanrenauto.findpic.findimg import *
from lanrenauto.tools.screenshot import screenshot
import state
from lanrenauto.yolov.lanrenonnx import LanRenOnnxYolov
from lanrenauto.moni.moni import *
import pyautogui
imgs_duihuahuopengren = [
            cv2.imdecode(np.fromfile(file="./datas/对话.png", dtype=np.uint8), cv2.IMREAD_UNCHANGED),
            cv2.imdecode(np.fromfile(file="./datas/烹饪.png", dtype=np.uint8), cv2.IMREAD_UNCHANGED),
            cv2.imdecode(np.fromfile(file="./datas/钓鱼.png", dtype=np.uint8), cv2.IMREAD_UNCHANGED),
            cv2.imdecode(np.fromfile(file="./datas/齿轮.png", dtype=np.uint8), cv2.IMREAD_UNCHANGED)
        ]
imgs_pianpianhua = [cv2.imdecode(np.fromfile(file="./datas/薄荷.png", dtype=np.uint8), cv2.IMREAD_UNCHANGED),
                    cv2.imdecode(np.fromfile(file="./datas/甜甜花.png", dtype=np.uint8), cv2.IMREAD_UNCHANGED)
                    ]
def 开伞():
    __t=time.time()
    if  -state.状态_开伞时间 > 2 or state.状态_开伞时间==-1:
        state.状态_开伞时间=__t
        tangbaowss.send_msg("是否回放#@@#假")
        logger.info("检测到可能在下落 打风之翼")
        pyautogui.keyDown('space')
        time.sleep(0.2)
        pyautogui.keyUp('space')
        time.sleep(0.4)
        pyautogui.keyDown('space')
        time.sleep(0.2)
        pyautogui.keyUp('space')
        state.计次_误差过大 += 1
        state.状态_开伞时间=-1
class YolovYuanShen():
    def __init__(self, weights, providers=["CUDAExecutionProvider",'CPUExecutionProvider'],dic_labels={},model_h=320, model_w=320):
        pyautogui.PAUSE = 0.01
        pyautogui.FAILSAFE = False
        self.yolov = LanRenOnnxYolov(weights=weights, providers=providers,dic_labels=dic_labels,model_h=model_h, model_w=model_w)
        self.interaction_label = ["怪物", "采集物", "矿石","掉落物"]
        self.centre_point = [(1920 // 2), (1080 // 2) + 100]
        self.key_w_down = True
        self.小地图区域 = (87 - 10, 45 - 10, 246 + 10, 204 + 10)
        state.状态_YOLOV = True
    def run_fight(self,wakuang=False,caiji=False,daguai=False):
        global loop
        state.计时_未寻路 = 0
        # 获取窗口句柄
        hwnd = win32gui.FindWindow("UnityWndClass", state.GAME_TITLE)  # 替换成你实际的窗口句柄
        while True:
            if state.状态_循环开关 == False:
                logger.info("强制退出!")
                pyautogui.keyUp('w')
                state.状态_是否回放中 = False
                state.计数_没找到任何目标 = 0
                tangbaowss.send_msg( "是否回放#@@#假")
                state.状态_检测中=False
                return
            if get_window_handle_at_mouse_position() == hwnd:
                break
        state.计数_没找到任何目标 = 0

        state.计数_没找到怪物 = 0
        state.状态_在爬墙 = False
        state.状态_检测中 = True
        state.状态_开伞时间 = -1
        已经_F了=False
        历史_上次玩家高度 = 0.1
        玩家高度 = 0.1
        计数_没有玩家 = 0
        isIn_wxl = False
        set_window_activate(hwnd)
        jiaoben = (os.path.abspath(os.path.join(state.PATH_JIAOBEN, state.LIANZHAO)))
        # 临时发送消息给指定客户端
        message = f"解析脚本#@@#{jiaoben}"
        tangbaowss.send_msg(message)
        time.sleep(2)

        while True:
            try:
                if state.状态_循环开关 == False:
                    state.状态_是否回放中 = False
                    state.计数_没找到任何目标 = 0
                    state.状态_检测中 = False
                    tangbaowss.send_msg( "是否回放#@@#假")
                    return
                time.sleep(0.005)
                rect = win32gui.GetWindowRect(hwnd)
                w_p, h_p = (rect[2] - rect[0]) - 1920, (rect[3] - rect[1]) - 1080
                # 设定截图区域的左上角坐标 (x, y) 和右下角坐标 (x, y)
                left, top, right, bottom = 0, 0, 1920, 1080  # 替换成你实际的区域坐标

                # F交互框位置
                left_dh, top_dh, right_dh, bottom_dh = [1161, 508, 1319, 572]  # 替换成你实际的区域坐标

                p_left = rect[0] + w_p
                p_top = rect[1] + h_p
                计数_没有玩家 += 1
                if state.状态_寻路中 == False:
                    state.计数_没找到任何目标 += 1
                    state.计数_没找到怪物 += 1
                state.状态_在爬墙 = False
                if state.状态_已经有寻路了 == False or  state.状态_检测中==False:
                    state.状态_是否回放中 = False
                    state.计数_没找到任何目标 = 0
                    tangbaowss.send_msg( "是否回放#@@#假")
                    state.状态_检测中 = False
                    return
                if state.状态_全局暂停:
                    tangbaowss.send_msg( "是否回放#@@#假")
                    time.sleep(2)
                    continue
                if get_window_handle_at_mouse_position() != hwnd:
                    tangbaowss.send_msg( "是否回放#@@#假")
                    time.sleep(0.1)
                    # 激活hwnd
                    hwnd = win32gui.FindWindow("UnityWndClass", state.GAME_TITLE)  # 替换成你实际的窗口句柄
                    set_window_activate(hwnd)

                    time.sleep(0.2)
                if state.状态_传送中:
                    time.sleep(1)
                    continue
                # 设定保存截图的文件夹路径和文件名前缀
                # filename = f"./原神截图/{int(time.time())}{random.randint(1000,9999)}.jpg"
                ret_scr = screenshot(hwnd, left, top, right, bottom, filename=None)
                if type(ret_scr) == bool:
                    hwnd = win32gui.FindWindow("UnityWndClass", state.GAME_TITLE)  # 替换成你实际的窗口句柄
                    logger.info("截图失败了!可能是内存溢出了")
                    time.sleep(2)
                    continue

                datas, state.图片_YOLOV = self.yolov.detect(ret_scr, plot_box=state.开关_是否展预测结果)
                if state.开关_是否展预测结果:
                    state.QT_信号.mysig_show_yolov.emit()
                if state.状态_全局暂停:
                    pyautogui.keyUp('w')
                    tangbaowss.send_msg( "是否回放#@@#假")
                    time.sleep(2)
                    continue
                if datas == []:

                    if state.状态_是否回放中 == False:
                        if state.状态_在爬墙 == False and state.状态_寻路中 == True:

                            计数_没有玩家 = 0
                            ttt = threading.Thread(target=开伞)

                            if state.python_var > 9:
                                ttt.daemon = True
                            else:
                                ttt.setDaemon(True)
                            ttt.start()

                else:
                    # 将离屏幕中心最近的点排在前面

                    datas = sorted(datas, key=self.distance_to_target)
                    isIn, idex = find_dict_index(datas, 'label', '往上跳')
                    if isIn:
                        state.状态_在爬墙 = True
                    else:
                        state.状态_在爬墙 = False
                    isIn_wxl, _ = find_dict_index(datas, 'label', '往下落')

                    isIn, idex = find_dict_index(datas, 'label', '被控了')
                    if isIn:
                        logger.info("被控了!")
                        pyautogui.press('space')
                        pyautogui.press('space')
                        state.状态_是否回放中 = False
                        tangbaowss.send_msg( "是否回放#@@#假")
                        continue
                    isIn_jhdh, idex = find_dict_index(datas, 'label', 'F交互')
                    if isIn_jhdh:  # 路过的顺手捡了
                        # 二次排除对话烹饪  并且识别骗骗花
                        if random.randint(1, 2) == 1:

                            pyautogui.scroll(300)
                        else:
                            pyautogui.scroll(-300)
                        pyautogui.keyUp('w')
                        if not self.find_duihuahuopengren(hwnd, left_dh, top_dh, right_dh, bottom_dh, p_left,
                                                          p_top) or self.find_pianpianhua(hwnd, left_dh, top_dh,
                                                                                          right_dh, bottom_dh, p_left,
                                                                                          p_top):

                            self.key_w_down = False
                            已经_F了 =True

                            state.计次_识别次数 = 0
                            state.计数_卡主次数 = 0
                            logger.info(f"顺手捡采集物!{datas[idex]}")
                            state.状态_有目标 = True
                            pyautogui.keyDown('f')
                            time.sleep(0.1)
                            pyautogui.keyUp('f')
                            time.sleep(0.1)
                            pyautogui.keyDown('f')
                            time.sleep(0.1)
                            pyautogui.keyUp('f')
                            time.sleep(0.5)
                            state.计次_识别次数 = 0
                            state.计数_卡主次数 = 0
                            ret_scr = screenshot(hwnd, left, top, right, bottom, filename=None)
                            datas, state.图片_YOLOV = self.yolov.detect(ret_scr, plot_box=state.开关_是否展预测结果)

                        else:
                            pyautogui.keyDown('w')
                            logger.info(f"是对话或烹饪{datas[idex]}")

                            isIn_jhdh = False
                    isIn, idex = find_dict_index(datas, 'label', '在水面')
                    if isIn:
                        logger.info("在水面!")
                        state.状态_是否回放中 = False
                        tangbaowss.send_msg( "是否回放#@@#假")
                        state.状态_寻路中 = True
                        self.key_w_down = True
                        if random.randint(1, 4) == 2:
                            pyautogui.keyDown('shiftleft')
                            time.sleep(0.6)
                            pyautogui.keyUp('shiftleft')

                    isIn, idex = find_dict_index(datas, 'label', '确定')
                    if isIn:
                        if state.状态_寻路中:
                            state.状态_寻路中 = False
                        state.状态_有目标 = True
                        state.状态_是否回放中 = False
                        if self.key_w_down:
                            self.key_w_down = False
                            pyautogui.keyUp('w')
                        tangbaowss.send_msg( "是否暂停#@@#真")
                        time.sleep(0.2)
                        mouse_move(p_left + datas[idex]["x"], p_top + datas[idex]["y"])
                        logger.info(f"发现确定按钮,可能是吃复活药{datas[idex]}")
                        time.sleep(0.1)
                        mouse_left_down()
                        time.sleep(0.1)
                        mouse_left_up()
                        time.sleep(0.5)
                        state.计数_卡主次数 = 0
                        state.计次_识别次数 = 0
                        ret_scr = screenshot(hwnd, left, top, right, bottom, filename=None)
                        datas, state.图片_YOLOV = self.yolov.detect(ret_scr, plot_box=state.开关_是否展预测结果)
                        isIn, idex = find_dict_index(datas, 'label', '确定')
                        tangbaowss.send_msg( "是否暂停#@@#假")
                        if not isIn:
                            logger.info("停止脚本")
                            tangbaowss.send_msg( "是否回放#@@#假")


                    isIn, idex = find_dict_index(datas, 'label', '取消')
                    if isIn:
                        if state.状态_寻路中:
                            state.状态_寻路中 = False
                        state.状态_是否回放中 = False
                        if self.key_w_down:
                            self.key_w_down = False
                            pyautogui.keyUp('w')

                        # 判断是否死亡
                        ret_sw = self.find_zhuyidiren(hwnd, left, top, right, bottom, p_left, p_top)
                        state.状态_有目标 = True
                        tangbaowss.send_msg( "是否暂停#@@#真")
                        time.sleep(0.2)
                        mouse_move(p_left + datas[idex]["x"], p_top + datas[idex]["y"])
                        logger.info(f"发现取消按钮,可能是吃复活药{datas[idex]}")
                        time.sleep(0.2)
                        mouse_left_down()
                        time.sleep(0.2)
                        mouse_left_up()
                        time.sleep(0.3)
                        pyautogui.keyDown('shift')
                        time.sleep(0.2)
                        pyautogui.keyUp('shift')
                        state.计数_卡主次数 = 0
                        state.计次_识别次数 = 0
                        tangbaowss.send_msg( "是否暂停#@@#假")
                        if ret_sw:
                            state.计次_移动停滞 = 1200
                            state.计次_定位失败 = 1200
                            state.状态_是否回放中 = False
                            state.计数_没找到任何目标 = 0
                            logger.info("全军覆没了!跳过该任务!")
                            pyautogui.keyDown('esc')
                            time.sleep(0.1)
                            pyautogui.keyUp('esc')
                            state.状态_检测中 = False
                            return
                        ret_scr = screenshot(hwnd, left, top, right, bottom, filename=None)
                        datas, state.图片_YOLOV = self.yolov.detect(ret_scr, plot_box=state.开关_是否展预测结果)


                    isIn, idex = find_dict_index(datas, 'label', '返回')
                    if isIn:
                        logger.info("在派蒙界面,点返回")
                        pyautogui.keyUp('w')
                        self.key_w_down = False
                        pyautogui.keyDown("esc")
                        time.sleep(0.2)
                        pyautogui.keyUp("esc")
                        time.sleep(2)
                        pyautogui.keyDown('shift')
                        time.sleep(0.2)
                        pyautogui.keyUp('shift')
                        continue
                    isIn, idex = find_dict_index(datas, 'label', '玩家')
                    if isIn:
                        玩家高度 = datas[idex]["points"][1][1] - datas[idex]["points"][0][1]
                        计数_没有玩家 = 0
                    isIn, idex = find_dict_index(datas, 'label', '关闭')
                    if isIn:
                        if state.状态_寻路中:
                            state.状态_寻路中 = False
                        state.状态_有目标 = True

                        logger.info(f"发现关闭按钮 点错了界面{datas[idex]}")
                        pyautogui.keyDown('esc')
                        time.sleep(0.2)
                        pyautogui.keyUp('esc')
                        time.sleep(1)
                        mouse_moveR(6 * 180, 0)
                        time.sleep(2)
                        pyautogui.keyDown("w")
                        time.sleep(0.1)
                        pyautogui.keyUp("w")
                        self.key_w_down = False
                        ret_scr = screenshot(hwnd, left, top, right, bottom, filename=None)
                        datas, state.图片_YOLOV = self.yolov.detect(ret_scr, plot_box=state.开关_是否展预测结果)
                    for data in datas:

                        if isIn_jhdh == True:
                            state.计数_卡主次数 = 0
                            state.计次_识别次数 = 0
                            break
                        # 发现怪物则进行视角移动 以及 接近目标
                        if data["label"] in self.interaction_label:
                            if state.状态_寻路中 == False:
                                # 将它的中心点和游戏正中间进行靠近
                                if data["label"] == "怪物"  and data["points"][1][0] - \
                                            data["points"][0][0] >= 70:
                                    if not daguai:
                                        continue
                                    state.计数_没找到任何目标 = 0
                                    state.计数_没找到怪物 = 0

                                    # 如果怪物就再旁边则直接连招
                                    if self.centre_point[1] - data["points"][1][1] <= 20:
                                        if self.key_w_down:
                                            self.key_w_down = False
                                            pyautogui.keyUp('w')

                                        state.计数_卡主次数 = 0
                                        state.计次_识别次数 = 0

                                        if not state.状态_是否回放中:
                                            mouse_moveR(0, 800)
                                            logger.info("在连招中....")
                                            pyautogui.keyDown('x')
                                            time.sleep(0.2)
                                            pyautogui.keyUp('x')
                                            state.状态_是否回放中 = True

                                            tangbaowss.send_msg( "脚本执行#@@#2")

                                    else:

                                        angle=get_angle(self.centre_point, (data["x"], data["y"]))
                                        angle_diff = abs(0 - angle)
                                        if state.状态_在爬墙==True:
                                            #如果角度大于45度则建议按X绕过柱子
                                            if angle_diff>45:
                                                pyautogui.keyDown('x')
                                                time.sleep(0.2)
                                                pyautogui.keyUp('x')
                                                set_angle(0, angle)
                                                if random.randint(1,2)==2:
                                                    pyautogui.keyDown('a')
                                                    time.sleep(0.2)
                                                    pyautogui.keyUp('a')
                                                else:
                                                    pyautogui.keyDown('d')
                                                    time.sleep(0.2)
                                                    pyautogui.keyUp('d')
                                            else:
                                                pyautogui.keyDown('space')
                                                time.sleep(0.2)
                                                pyautogui.keyUp('space')
                                        else:
                                            set_angle(0, angle)
                                        pyautogui.keyDown('w')
                                        if random.randint(0, 50) == 5:
                                            pyautogui.keyDown('space')
                                            time.sleep(0.2)
                                            pyautogui.keyUp('space')
                                        self.key_w_down = True

                                    break  # 只判断第一只怪
                                elif data["label"] == "矿石":
                                    if not wakuang:
                                        continue
                                    state.计数_没找到任何目标 = 0
                                    state.计数_没找到怪物 = 0

                                    set_angle(0, get_angle(self.centre_point, (data["x"], data["y"])))
                                    # 如果矿石就再旁边则直接连招
                                    if self.centre_point[1] - data["points"][1][1] <= 40:
                                        if self.key_w_down:
                                            self.key_w_down = False
                                            time.sleep(0.2)
                                            pyautogui.keyUp('w')
                                        logger.info("在挖矿中....")

                                        state.计数_卡主次数 = 0
                                        state.计次_识别次数 = 0
                                        if not state.状态_是否回放中:
                                            logger.info("在挖矿中....")
                                            pyautogui.press("x")
                                            state.状态_是否回放中 = True
                                            tangbaowss.send_msg( "脚本执行#@@#2")

                                    else:

                                        pyautogui.keyDown('w')
                                        if random.randint(0, 50) == 5:
                                            pyautogui.keyDown('space')
                                            time.sleep(0.2)
                                            pyautogui.keyUp('space')
                                        self.key_w_down = True

                                    break  # 只判断第1个矿
                                else:  # '发现采集物'或 "掉落物"

                                    state.计数_没找到任何目标 = 0
                                    angle_now = 0
                                    angle_target = get_angle(self.centre_point, (data["x"], data["y"]))
                                    r_caiji=set_angle(angle_now, angle_target)
                                    pyautogui.keyDown('w')
                                    if random.randint(0, 30) == 5:
                                        pyautogui.keyDown('space')
                                        pyautogui.keyUp('space')
                                        self.key_w_down = True

                                    if  r_caiji==False:
                                        pyautogui.keyDown('shift')
                                        time.sleep(0.2)
                                        pyautogui.keyUp('shift')
                                    break  # 只判断第一个采集物
                            else:
                                if data["sim"]>=0.6:
                                    if state.状态_在爬墙==True:
                                        continue
                                    if data["label"] == "矿石":
                                        if not wakuang:
                                            continue
                                        logger.info("发现有矿石.顺手挖一下吧!")
                                        已经_F了 = False
                                    elif data["label"] == "怪物" and data["points"][1][
                                        0] - data["points"][0][0] >= 90:
                                        if not daguai:
                                            continue
                                        已经_F了 = False
                                        logger.info("发现有怪拦路.顺手消灭吧!")
                                    elif data["label"] == "采集物":
                                        if not caiji:
                                            continue
                                        已经_F了 = False
                                        logger.info("发现有采集物.顺手捡一下吧!")
                                    elif data["label"] =="掉落物":
                                        if  daguai or wakuang or caiji:

                                            已经_F了 = False
                                            logger.info("发现有掉落物.顺手捡一下吧!")
                                        else:
                                            continue

                                    else:
                                        continue
                                    set_angle(0, get_angle(self.centre_point, (data["x"], data["y"])))
                                    state.计时_未寻路 = int(time.time())
                                    state.状态_寻路中 = False
                                    state.游戏_打怪前坐标 = copy.copy(state.游戏_当前目标坐标)
                                    state.计数_没找到任何目标 = 0
                                    state.计数_没找到怪物 = 0
                                    pyautogui.keyUp('w')
                                    self.key_w_down = False
                                    state.计数_卡主次数 = 0
                                    state.计次_识别次数 = 0
                                    mouse_left_down()
                                    time.sleep(0.1)
                                    mouse_left_up()
                                    break
                # print("state.计时_未寻路", state.计时_未寻路, "state.计数_没找到任何目标", state.计数_没找到任何目标,
                # "state.计数_没找到怪物", state.计数_没找到怪物)
                # 如果玩家突然不见了,说明正在快速下落的概率很大,则开伞

                if state.状态_寻路中 == True and state.状态_在爬墙 == False and isIn_wxl == False:

                    if 计数_没有玩家 >= 3 or (历史_上次玩家高度 / 玩家高度 >= 1.5):

                        计数_没有玩家 = 0
                        ttt = threading.Thread(target=开伞)

                        if state.python_var > 9:
                            ttt.daemon = True
                        else:
                            ttt.setDaemon(True)
                        ttt.start()

                if 计数_没有玩家 == 0:
                    历史_上次玩家高度 = copy.copy(玩家高度)
                if state.状态_寻路中 == False:
                    # 如果按下了W键了,并且当前没有任何目标等于4次了,则松开w键
                    if state.计数_没找到任何目标 == 6:
                        self.key_w_down = False
                        state.计次_识别次数 = 0
                        state.计数_卡主次数 = 0
                        pyautogui.keyUp('w')
                    if state.计次_定位失败 > 0:
                        state.计数_没找到怪物 = 0

                    if state.ON_LIANZHAOBUJIANCE==0 and wakuang==False and state.计数_没找到怪物 >= 25 and state.计次_定位失败 == 0 and state.状态_是否回放中 == True:
                        state.状态_是否回放中 = False
                        tangbaowss.send_msg( "是否回放#@@#假")

                    # 如果连续8次没找到目标,则进行旋转
                    if 20 > state.计数_没找到任何目标 >= 15 or (
                            state.计数_没找到任何目标 >= 1 and state.计数_没找到怪物 >= 10 and state.状态_是否回放中 == True):
                        state.计数_卡主次数 = 0
                        state.计次_识别次数 = 0
                        历史_上次玩家高度 = 0
                        if state.计数_没找到任何目标 == 15:
                            if state.计次_定位失败 == 0:
                                if state.状态_是否回放中 == False:
                                    logger.info("重置视角")
                                    pyautogui.keyUp('w')
                                    self.key_w_down = False
                                    pyautogui.keyDown('x')
                                    time.sleep(0.1)
                                    pyautogui.keyUp('x')
                                    # 重置镜头
                                    mouse_middle_down()
                                    time.sleep(0.2)
                                    mouse_middle_up()

                                    time.sleep(0.3)
                                    mouse_moveR(0, 600)
                                    state.计次_识别次数 = 0
                                    state.计数_卡主次数 = 0
                                else:
                                    state.计数_没找到任何目标 = 0
                            else:
                                state.计数_没找到任何目标 = 0
                        else:

                            mouse_moveR(700, 0)
                            time.sleep(0.1)

                    elif state.计数_没找到任何目标 == 13 and state.状态_是否回放中 == False:
                        mouse_moveR(0, 800)
                        time.sleep(0.1)
                    elif state.计数_没找到任何目标 == 10 and state.状态_是否回放中 == False:
                        if not 已经_F了:
                            pyautogui.keyDown('shift')
                            time.sleep(0.2)
                            pyautogui.keyUp('shift')


                    # 如果连续18次没找到目标,则继续寻路X
                    elif state.计数_没找到任何目标 >= 20:
                        if  not state.状态_是否回放中:
                            logger.info("附近没有怪和采集物了.继续寻路吧!")
                            state.状态_是否回放中 = False
                            tangbaowss.send_msg( "是否回放#@@#假")
                            state.状态_寻路中 = True
                            state.计次_识别次数 = 0
                            state.计数_卡主次数 = 0
                            state.计数_没找到任何目标 = 0
                            mouse_left_up()
                            pyautogui.keyUp('w')
                            continue

                    time_now=time.time()
                    if time_now-state.计时_未寻路 >= state.TIMEOUT_DAGUAI:



                        kkk = self.处理异常情况(hwnd, left, top, right, bottom, p_left, p_top)
                        if kkk == 0:
                            logger.info("在意外界面 超时!")
                            time.sleep(3)
                            state.状态_检测中 = False
                            state.状态_已经有寻路了 = False
                            state.状态_需重新传送 = False
                            state.计次_移动停滞 = 1200
                            state.计次_定位失败 = 1200
                            state.状态_是否回放中 = False
                            state.计数_没找到任何目标 = 0
                            return False
                        elif kkk == -1:
                            logger.info("全军覆没了!")
                            time.sleep(3)
                            state.计次_移动停滞 = 1200
                            state.计次_定位失败 = 1200
                            state.状态_是否回放中 = False
                            state.计数_没找到任何目标 = 0
                            state.状态_检测中 = False
                            state.状态_已经有寻路了 = False
                            state.状态_需重新传送 = True
                            return False
                        if state.计时_未寻路 != 0:

                            logger.info("附近可能还有怪,但是打不到.继续寻路吧!")
                            state.状态_是否回放中 = False
                            tangbaowss.send_msg( "是否回放#@@#假")
                            state.状态_寻路中 = True

                            state.计数_卡主次数 = 0
                            mouse_left_up()
                            pyautogui.keyUp('w')
                            state.计数_没找到任何目标 = 0
                            state.游戏_打怪前坐标 = [0, 0]
                            time.sleep(5)
                        else:

                            state.状态_是否回放中 = False
                            tangbaowss.send_msg("是否回放#@@#假")
                            state.状态_寻路中 = True

                            state.计数_卡主次数 = 0
                            mouse_left_up()
                            pyautogui.keyUp('w')
                            state.计数_没找到任何目标 = 0
                            state.游戏_打怪前坐标 = [0, 0]
                        continue
                    elif time_now-state.计时_未寻路 == state.TIMEOUT_DAGUAI-15  and state.状态_是否回放中==False and state.计数_没找到怪物>=1:
                        mouse_left_up()
                        logger.info("可能是有矿石 连招中....")
                        if random.randint(1,3)==2:
                            pyautogui.press("x")
                        state.状态_是否回放中 = True
                        tangbaowss.send_msg( "脚本执行#@@#2")
                    elif state.TIMEOUT_DAGUAI//2<= time_now-state.计时_未寻路 >= state.TIMEOUT_DAGUAI//2-10 and state.状态_是否回放中==False and state.计数_没找到怪物==0:
                        mouse_left_up()
                        logger.info("可能是飞行物 连招中....")
                        if random.randint(1,3)==2:
                            pyautogui.press("x")
                        state.状态_是否回放中 = True
                        tangbaowss.send_msg( "脚本执行#@@#2")


            except:
                time.sleep(0.5)
                logger.error(traceback.format_exc())
    def run_fuben(self, cishu=99, isFanxiang=False):
        global loop
        state.状态_已经有寻路了 = True
        # 获取窗口句柄
        hwnd = win32gui.FindWindow("UnityWndClass", state.GAME_TITLE)  # 替换成你实际的窗口句柄
        set_window_activate(hwnd)
        while True:
            if state.状态_循环开关 == False:
                logger.info("强制退出!")
                pyautogui.keyUp('w')
                state.状态_已经有寻路了 = False
                state.状态_是否回放中 = False
                tangbaowss.send_msg( "是否回放#@@#假")
                state.状态_需重新传送 = False
                return False
            if get_window_handle_at_mouse_position() == hwnd:
                break

        rect = win32gui.GetWindowRect(hwnd)
        w_p, h_p = (rect[2] - rect[0]) - 1920, (rect[3] - rect[1]) - 1080
        # 设定截图区域的左上角坐标 (x, y) 和右下角坐标 (x, y)
        left, top, right, bottom = 0, 0, 1920, 1080  # 替换成你实际的区域坐标

        p_left = rect[0] + w_p
        p_top = rect[1] + h_p



        paimeng_img = cv2.imdecode(np.fromfile(file="./datas/派蒙.png", dtype=np.uint8), cv2.IMREAD_UNCHANGED)
        kkk = self.处理异常情况(hwnd, left, top, right, bottom, p_left, p_top)
        if kkk == 0:
            logger.info("在意外界面 超时!")
            time.sleep(3)
            state.状态_需重新传送 = False
            state.状态_已经有寻路了 = False
            return False
        elif kkk == -1:
            logger.info("全军覆没了!")
            time.sleep(3)
            state.状态_需重新传送 = True
            state.状态_已经有寻路了 = False
            return False


        计数_没有玩家 = 0
        jiaoben = (os.path.abspath(os.path.join(state.PATH_JIAOBEN, state.LIANZHAO)))
        # 临时发送消息给指定客户端
        message = f"解析脚本#@@#{jiaoben}"
        tangbaowss.send_msg(message)
        try:
            # 往前走或者往后走,直到找到 F
            if not self.find_f_keep_going(hwnd, left, top, right, bottom, p_left, p_top,
                                          isFanxiang): state.状态_需重新传送 = True;return False
            # 点击 单人挑战
            if not self.click_danrentiaozhan(hwnd, left, top, right, bottom, p_left,
                                             p_top): state.状态_需重新传送 = True;return False
            # 判断弹窗是否有树脂不够提醒
            if self.find_shuzhibugou(hwnd, left, top, right, bottom, p_left, p_top):
                pyautogui.keyDown('esc')
                time.sleep(0.1)
                pyautogui.keyUp('esc')
                time.sleep(2)
                pyautogui.keyDown('esc')
                time.sleep(0.1)
                pyautogui.keyUp('esc')

                state.状态_已经有寻路了 = False
                logger.info("没体力了 成功退出副本!")
                time.sleep(10)
                state.状态_需重新传送 = False
                return True
            # 如果没提醒则选择阵容
            if not self.click_kaishitiaozhan(hwnd, left, top, right, bottom, p_left, p_top):
                pyautogui.keyDown('esc')
                time.sleep(0.1)
                pyautogui.keyUp('esc')
                state.状态_需重新传送 = True
                state.状态_已经有寻路了 = False
                return False
            # 开始yolov自动进入副本
        except:
            time.sleep(0.5)
            logger.error(traceback.format_exc())
            state.状态_需重新传送 = True
            state.状态_已经有寻路了 = False
            return False
        # 0 镜头对准北方向 走过去开副本
        # 1 开始打怪阶段
        # 2 打完了  开始找生命树 并且把自己移动到0° 对准生命树的位置
        # 3 走路到 领取奖励  直到点击 继续挑战为止 完成一轮
        阶段 = 0
        计次_阶段0 = 0
        计时_阶段3_开始时间 = 0
        state.计数_没找到怪物=0
        isinF = False
        for i in range(cishu):
            logger.info(f"刷副本中  {i + 1}/{cishu}")
            while True:
                try:
                    time.sleep(0.01)
                    if state.状态_循环开关 == False:
                        state.状态_是否回放中 = False
                        tangbaowss.send_msg( "是否回放#@@#假")
                        state.状态_需重新传送 = False
                        state.状态_已经有寻路了 = False
                        return False

                    rect = win32gui.GetWindowRect(hwnd)
                    w_p, h_p = (rect[2] - rect[0]) - 1920, (rect[3] - rect[1]) - 1080
                    # 设定截图区域的左上角坐标 (x, y) 和右下角坐标 (x, y)
                    left, top, right, bottom = 0, 0, 1920, 1080  # 替换成你实际的区域坐标

                    p_left = rect[0] + w_p
                    p_top = rect[1] + h_p


                    if state.状态_全局暂停:
                        tangbaowss.send_msg( "是否回放#@@#假")
                        time.sleep(2)
                        continue
                    if get_window_handle_at_mouse_position() != hwnd:
                        # 激活hwnd
                        hwnd = win32gui.FindWindow("UnityWndClass", state.GAME_TITLE)  # 替换成你实际的窗口句柄
                        set_window_activate(hwnd)
                    # 设定保存截图的文件夹路径和文件名前缀
                    # filename = f"./datas/lanrenyolov{random.randint(1, 20)}.jpg"
                    # 游戏全图
                    ret_scr = screenshot(hwnd, left, top, right, bottom, filename=None)
                    if type(ret_scr) == bool:
                        hwnd = win32gui.FindWindow("UnityWndClass", state.GAME_TITLE)  # 替换成你实际的窗口句柄
                        logger.info("截图失败了!可能是内存溢出了")
                        time.sleep(2)
                        continue
                    # 这里是小地图的截图
                    left_small, top_small, right_small, bottom_small = self.小地图区域  # 替换成你实际的区域坐标
                    height_small = bottom_small - top_small
                    width_small = right_small - left_small
                    small_img = screenshot(hwnd, left_small, top_small, right_small, bottom_small,
                                           filename=None)  # RGBA
                    if type(small_img) == bool:
                        hwnd = win32gui.FindWindow("UnityWndClass", state.GAME_TITLE)  # 替换成你实际的窗口句柄
                        time.sleep(2)
                        continue
                    state.游戏_当前视野角度 = get_view2(small_img[int(height_small / 2) - 70:int(height_small / 2) + 70,
                                                        int(width_small / 2) - 70:int(width_small / 2) + 70], 70)
                    # -----------------------------YOLOV检测--------------------------------------------------
                    datas, state.图片_YOLOV = self.yolov.detect(ret_scr, plot_box=state.开关_是否展预测结果)
                    if state.开关_是否展预测结果:
                        state.QT_信号.mysig_show_yolov.emit()

                    if state.状态_全局暂停:
                        tangbaowss.send_msg( "是否回放#@@#假")
                        time.sleep(2)
                        continue
                    if datas == []:
                        pass
                    else:
                        # 将离屏幕中心最近的点排在前面
                        datas = sorted(datas, key=self.distance_to_target)
                        if 阶段 == 0:  # 0 镜头对准北方向 走过去开副本
                            计次_阶段0 += 1
                            if 计次_阶段0 >= 2000:
                                pyautogui.keyDown('esc')
                                time.sleep(0.1)
                                pyautogui.keyUp('esc')
                                time.sleep(1)
                                state.状态_已经有寻路了 = False
                                logger.info("没体力了 成功退出副本!")
                                time.sleep(10)
                                state.状态_需重新传送 = False
                                return True

                            isIn, idex = find_dict_index(datas, 'label', '提示关闭')
                            if isIn:  # 路过的顺手捡了
                                logger.info("发现 提示关闭")
                                mouse_move(p_left + datas[idex]["x"], p_top + datas[idex]["y"])
                                time.sleep(0.2)
                                mouse_left_down()
                                time.sleep(0.2)
                                mouse_left_up()
                                time.sleep(1)
                                continue

                            if self.click_dimaiyichang(hwnd, left, top, right, bottom, p_left, p_top):
                                time.sleep(1)
                                continue

                            if not set_angle(state.游戏_当前视野角度, 90, fault=5):
                                logger.info("调整整好")
                                # 角度对了 开始一路往前走 直到找到F按钮
                                pyautogui.keyDown("w")
                                if not self.find_f_keep_going(hwnd, left, top, right, bottom, p_left, p_top,
                                                              False): continue
                                pyautogui.keyUp("w")
                                阶段 = 1
                            isIn, idex = find_dict_index(datas, 'label', '确定')
                            isIn2, idex = find_dict_index(datas, 'label', '取消')
                            # 判断弹窗是否有树脂不够提醒  如果发现 则点击退出返回 阶段4走起
                            if isIn or isIn2:
                                pyautogui.keyDown('esc')
                                time.sleep(0.1)
                                pyautogui.keyUp('esc')
                                time.sleep(1)
                                state.状态_已经有寻路了 = False
                                logger.info("没体力了 成功退出副本!")
                                time.sleep(10)
                                state.状态_需重新传送 = True
                                return True
                        elif 阶段 == 1:  # 1 开始打怪阶段
                            isinF = False
                            计次_阶段0 = 0
                            # 判断是否打完了副本
                            isIn, idex = find_dict_index(datas, 'label', '副本打完了')
                            #or self.find_zidongtuichu(hwnd, 745,905,1206,1002, p_left, p_top)
                            if isIn :
                                if idex!=None:
                                    if datas[idex]["sim"]>0.75:
                                        self.key_w_down = False
                                        pyautogui.keyUp('w')
                                        mouse_left_up()
                                        state.状态_是否回放中 = False
                                        tangbaowss.send_msg( "是否回放#@@#假")
                                        logger.info("打完了")
                                        time.sleep(2)
                                        # 重置镜头
                                        mouse_middle_down()
                                        time.sleep(0.1)
                                        mouse_middle_up()
                                        阶段 = 2
                                        continue
                                else:
                                    self.key_w_down = False
                                    pyautogui.keyUp('w')
                                    mouse_left_up()
                                    state.状态_是否回放中 = False
                                    tangbaowss.send_msg( "是否回放#@@#假")
                                    logger.info("打完了")
                                    time.sleep(2)
                                    # 重置镜头
                                    mouse_middle_down()
                                    time.sleep(0.1)
                                    mouse_middle_up()
                                    阶段 = 2
                                    continue

                            isIn, idex = find_dict_index(datas, 'label', '往上跳')
                            if isIn:
                                logger.info("尝试跳起来")
                                pyautogui.keyDown('space')
                                time.sleep(0.1)
                                pyautogui.keyUp('space')

                            isIn, idex = find_dict_index(datas, 'label', '被控了')
                            if isIn:
                                logger.info("被控了!")
                                pyautogui.keyDown('space')
                                time.sleep(0.1)
                                pyautogui.keyUp('space')
                                time.sleep(0.1)
                                pyautogui.keyDown('space')
                                time.sleep(0.1)
                                pyautogui.keyUp('space')
                                time.sleep(0.1)
                                pyautogui.keyDown('space')
                                time.sleep(0.1)
                                pyautogui.keyUp('space')
                                time.sleep(0.1)
                                pyautogui.keyDown('space')
                                time.sleep(0.1)
                                pyautogui.keyUp('space')
                            isIn, idex = find_dict_index(datas, 'label', '确定')
                            if isIn:

                                tangbaowss.send_msg( "是否暂停#@@#真")
                                time.sleep(0.2)
                                mouse_move(p_left + datas[idex]["x"], p_top + datas[idex]["y"])
                                logger.info(f"发现确定按钮,可能是吃复活药{datas[idex]}")
                                time.sleep(0.1)
                                mouse_left_down()
                                time.sleep(0.1)
                                mouse_left_up()
                                time.sleep(0.5)
                                ret_scr = screenshot(hwnd, left, top, right, bottom, filename=None)
                                datas, state.图片_YOLOV = self.yolov.detect(ret_scr, plot_box=state.开关_是否展预测结果)
                                isIn, idex = find_dict_index(datas, 'label', '确定')
                                tangbaowss.send_msg( "是否暂停#@@#假")
                                if not isIn:
                                    logger.info("停止脚本")
                                    tangbaowss.send_msg( "是否回放#@@#假")
                            isIn, idex = find_dict_index(datas, 'label', '取消')
                            if isIn:

                                tangbaowss.send_msg( "是否暂停#@@#真")
                                time.sleep(0.2)
                                mouse_move(p_left + datas[idex]["x"], p_top + datas[idex]["y"])
                                logger.info(f"发现取消按钮,可能是吃复活药{datas[idex]}")
                                time.sleep(0.2)
                                mouse_left_down()
                                time.sleep(0.2)
                                mouse_left_up()
                                time.sleep(0.3)
                                pyautogui.keyDown('shift')
                                time.sleep(0.2)
                                pyautogui.keyUp('shift')
                                tangbaowss.send_msg( "是否暂停#@@#假")
                                ret_scr = screenshot(hwnd, left, top, right, bottom, filename=None)
                                datas, state.图片_YOLOV = self.yolov.detect(ret_scr, plot_box=state.开关_是否展预测结果)
                            isIn, idex = find_dict_index(datas, 'label', '怪物')
                            if not isIn:
                                state.计数_没找到怪物+=1
                                if random.randint(1, 50) == 2:
                                    mouse_middle_down()
                                    time.sleep(0.2)
                                    mouse_middle_up()
                                if state.计数_没找到怪物>=20:
                                    tangbaowss.send_msg( "是否回放#@@#假")
                                    state.状态_是否回放中 = False
                                    pyautogui.keyUp('w')
                                    self.key_w_down=False
                                    mouse_moveR(int(6 * 120), 0)
                                    time.sleep(0.1)

                                if template_matching(ret_scr, paimeng_img, mask=None) != []:
                                    logger.info("全军覆没了!跳重试该任务!")
                                    state.状态_需重新传送 = True
                                    state.状态_已经有寻路了 = False
                                    return False
                            else:
                                state.计数_没找到怪物=0

                                for data in datas:
                                    # 将它的中心点和游戏正中间进行靠近
                                    if data["label"] == "怪物":
                                        set_angle(0, get_angle(self.centre_point, (data["x"], data["y"])))
                                        # 如果怪物就再旁边则直接连招
                                        if self.centre_point[1] - data["points"][1][1] <= 80 and data["points"][1][0] - \
                                                data["points"][0][0] >= 90:
                                            if self.key_w_down:
                                                self.key_w_down = False
                                                pyautogui.keyUp('w')
                                            if not state.状态_是否回放中:
                                                logger.info("在连招中....")
                                                pyautogui.press("x")
                                                state.状态_是否回放中 = True
                                                tangbaowss.send_msg( "脚本执行#@@#2")
                                        else:
                                            # 不在则继续跟着怪物方向走
                                            pyautogui.keyDown('w')
                                            self.key_w_down = True
                                        break
                        elif 阶段 == 2:  # 2 打完了  开始找生命树 并且把自己移动到0° 对准生命树的位置
                            调整视角 = set_angle(state.游戏_当前视野角度, 90, fault=6)
                            发现了目标了 = False
                            isIn, idex = find_dict_index(datas, 'label', '生命树')
                            for data in datas:
                                if data["label"] == "生命树":
                                    发现了目标了 = True
                                    x_p = data["x"] - self.centre_point[0]
                                    if x_p >= 16:
                                        pyautogui.keyUp('a')
                                        logger.info("树偏右边 按下D键")
                                        pyautogui.keyDown('d')
                                    elif x_p <= -16:
                                        pyautogui.keyUp('d')
                                        logger.info("树偏左边 按下A键")
                                        pyautogui.keyDown('a')
                                    else:
                                        if 调整视角 == False:
                                            pyautogui.keyUp('d')
                                            pyautogui.keyUp('a')
                                            logger.info("树正对,走起!")
                                            pyautogui.keyDown('w')
                                            阶段 = 3
                                            计时_阶段3_开始时间 = time.time()
                                            break
                                        logger.info("树正对 但是角度还不对")
                                elif data["label"] == "副本楼梯":
                                    发现了目标了 = True
                                    x_p = data["x"] - self.centre_point[0]
                                    if x_p >= 16:
                                        pyautogui.keyUp('a')
                                        logger.info("副本楼梯偏右边 按下D键")
                                        pyautogui.keyDown('d')
                                    elif x_p <= -16:
                                        pyautogui.keyUp('d')
                                        logger.info("副本楼梯偏左边 按下A键")
                                        pyautogui.keyDown('a')
                                    else:
                                        if 调整视角 == False:
                                            pyautogui.keyUp('d')
                                            pyautogui.keyUp('a')
                                            logger.info("副本楼梯正对,走起!")
                                            pyautogui.keyDown('w')
                                            阶段 = 3
                                            计时_阶段3_开始时间=time.time()
                                            break
                                        logger.info("副本楼梯正对 但是角度还不对")
                                elif data["label"] == "副本门框" and isIn == False:
                                    发现了目标了 = True
                                    x_p = data["x"] - self.centre_point[0]
                                    if x_p >= 16:
                                        pyautogui.keyUp('a')
                                        logger.info("副本门框偏右边 按下D键")
                                        pyautogui.keyDown('d')
                                    elif x_p <= -16:
                                        pyautogui.keyUp('d')
                                        logger.info("副本门框偏左边 按下A键")
                                        pyautogui.keyDown('a')
                            if 阶段 == 3:
                                continue
                            if 发现了目标了 == False:
                                rrrr = random.randint(1, 20)
                                if rrrr == 1:
                                    pyautogui.keyDown('s')
                                    pyautogui.keyDown('a')
                                    pyautogui.keyDown('space')
                                    time.sleep(0.1)
                                    pyautogui.keyUp('space')
                                    time.sleep(0.2)
                                    pyautogui.keyUp('a')
                                    pyautogui.keyUp('s')
                                elif rrrr == 2:
                                    pyautogui.keyDown('s')
                                    pyautogui.keyDown('a')
                                    pyautogui.keyDown('space')
                                    time.sleep(0.1)
                                    pyautogui.keyUp('space')
                                    time.sleep(0.2)
                                    pyautogui.keyUp('a')
                                    pyautogui.keyUp('s')
                                elif rrrr == 3:
                                    pyautogui.keyDown('w')
                                    pyautogui.keyDown('a')
                                    pyautogui.keyDown('space')
                                    time.sleep(0.1)
                                    pyautogui.keyUp('space')
                                    time.sleep(0.2)
                                    pyautogui.keyUp('a')
                                    pyautogui.keyUp('w')
                                elif rrrr == 4:
                                    pyautogui.keyDown('w')
                                    pyautogui.keyDown('d')
                                    pyautogui.keyDown('space')
                                    time.sleep(0.1)
                                    pyautogui.keyUp('space')
                                    time.sleep(0.2)
                                    pyautogui.keyUp('d')
                                    pyautogui.keyUp('w')
                                else:
                                    pyautogui.keyDown('s')
                                    time.sleep(0.1)
                                    pyautogui.keyUp('s')
                        elif 阶段 == 3:  # 3 走路到 领取奖励  直到点击 继续挑战为止 完成一轮
                            pyautogui.keyDown('w')
                            isIn, idex = find_dict_index(datas, 'label', 'F交互')
                            if isIn:  # 路过的顺手捡了
                                logger.info("发现 F 交互")
                                pyautogui.keyDown('f')
                                time.sleep(0.1)
                                pyautogui.keyUp('f')
                                isinF = True
                                continue

                            isIn, idex = find_dict_index(datas, 'label', '捡东西')
                            if isIn:  # 路过的顺手捡了
                                logger.info("发现 F 交互")
                                pyautogui.keyDown('f')
                                time.sleep(0.1)
                                pyautogui.keyUp('f')
                                isinF = True
                                continue

                            isIn, idex = find_dict_index(datas, 'label', '生命树')
                            if isIn and isinF==False :  # 路过的顺手捡了
                                set_angle(0, get_angle(self.centre_point, (datas[idex]["x"], datas[idex]["y"])))


                            isIn, idex = find_dict_index(datas, 'label', '使用浓缩树脂')
                            if isIn:  # 路过的顺手捡了
                                if datas[idex]['sim'] > 0.8:
                                    logger.info("发现 使用浓缩树脂")

                                    logger.info(str(datas[idex]))
                                    mouse_move(p_left + datas[idex]["x"], p_top + datas[idex]["y"])
                                    time.sleep(0.2)
                                    mouse_left_down()
                                    time.sleep(0.2)
                                    mouse_left_up()
                                    time.sleep(0.5)
                                    continue
                            isIn, idex = find_dict_index(datas, 'label', '使用原粹树脂')
                            if isIn:  # 路过的顺手捡了
                                if datas[idex]['sim'] > 0.8:
                                    logger.info("发现 使用原粹树脂")
                                    logger.info(str(datas[idex]))
                                    mouse_move(p_left + datas[idex]["x"], p_top + datas[idex]["y"])
                                    time.sleep(0.2)
                                    mouse_left_down()
                                    time.sleep(0.2)
                                    mouse_left_up()
                                    time.sleep(0.5)
                                    continue
                            isIn, idex = find_dict_index(datas, 'label', '继续挑战')

                            if isIn or self.find_jixutiaozhan(hwnd, 953,923,1432,1065, p_left, p_top):  # 路过的顺手捡了
                                if idex!=None:
                                    if datas[idex]['sim'] > 0.75:
                                        logger.info("发现 继续挑战")
                                        mouse_move(p_left + 1178, p_top + 1006)
                                        time.sleep(0.2)
                                        mouse_left_down()
                                        time.sleep(0.2)
                                        mouse_left_up()
                                        time.sleep(0.5)
                                        mouse_left_down()
                                        time.sleep(0.2)
                                        mouse_left_up()
                                        time.sleep(2)

                                        # 判断弹窗是否有树脂不够提醒  如果发现 则点击退出返回 阶段4走起
                                        if self.find_shuzhibugou2(hwnd, left, top, right, bottom, p_left, p_top):
                                            pyautogui.keyDown('esc')
                                            time.sleep(0.1)
                                            pyautogui.keyUp('esc')
                                            time.sleep(1)
                                            state.状态_已经有寻路了 = False
                                            logger.info("没体力了 成功退出副本!")
                                            time.sleep(10)
                                            state.状态_需重新传送 = False
                                            return True

                                        else:
                                            isinF = False
                                            阶段 = 0
                                            break

                                else:

                                    logger.info("发现 继续挑战")
                                    mouse_move(p_left + 1178, p_top + 1006)
                                    time.sleep(0.2)
                                    mouse_left_down()
                                    time.sleep(0.2)
                                    mouse_left_up()
                                    time.sleep(0.5)
                                    mouse_left_down()
                                    time.sleep(0.2)
                                    mouse_left_up()
                                    time.sleep(2)

                                    # 判断弹窗是否有树脂不够提醒  如果发现 则点击退出返回 阶段4走起
                                    if self.find_shuzhibugou2(hwnd, left, top, right, bottom, p_left, p_top):
                                        pyautogui.keyDown('esc')
                                        time.sleep(0.1)
                                        pyautogui.keyUp('esc')
                                        time.sleep(1)
                                        state.状态_已经有寻路了 = False
                                        logger.info("没体力了 成功退出副本!")
                                        time.sleep(10)
                                        state.状态_需重新传送 = False
                                        return True

                                    else:
                                        isinF = False
                                        阶段 = 0
                                        break
                            if time.time() - 计时_阶段3_开始时间 > 30:
                                rand_cz = random.randint(0, 4)
                                if rand_cz == 0:
                                    pyautogui.keyDown('x')
                                    time.sleep(0.3)
                                    pyautogui.keyUp('x')
                                    pyautogui.keyUp('w')
                                    pyautogui.keyDown('d')
                                    time.sleep(0.5)
                                    pyautogui.keyUp('d')
                                    pyautogui.keyDown('w')
                                elif rand_cz == 1:
                                    pyautogui.keyDown('x')
                                    time.sleep(0.3)
                                    pyautogui.keyUp('x')
                                    pyautogui.keyUp('w')
                                    pyautogui.keyDown('a')
                                    time.sleep(0.5)
                                    pyautogui.keyUp('a')
                                    pyautogui.keyDown('w')
                                elif rand_cz == 2:
                                    pyautogui.keyDown('x')
                                    time.sleep(0.3)
                                    pyautogui.keyUp('x')
                                elif rand_cz == 3:
                                    pyautogui.keyDown('x')
                                    time.sleep(0.3)
                                    pyautogui.keyUp('x')
                                    pyautogui.keyUp('w')
                                    pyautogui.keyDown('s')
                                    time.sleep(3)
                                    pyautogui.keyUp('s')
                                    pyautogui.keyDown('w')
                                else:
                                    pyautogui.keyDown('space')
                                    time.sleep(0.3)
                                    pyautogui.keyDown('space')
                                计时_阶段3_开始时间 = time.time()

                except:
                    logger.error(traceback.format_exc())
                    time.sleep(0.5)

        state.状态_已经有寻路了 = False
        logger.info("刷副本全部完成!")
    def run_chuansongjiaoben(self, jiaoben,cbb_address="手动录制"):
        global new_msg
        try:
            hwnd = win32gui.FindWindow("UnityWndClass", state.GAME_TITLE)  # 替换成你实际的窗口句柄

            if state.状态_循环开关 == False:
                tangbaowss.send_msg( "是否回放#@@#假")
                logger.info("强制退出!")
                return False
            if get_window_handle_at_mouse_position() == hwnd:
                set_window_activate(hwnd)
                time.sleep(0.2)
            rect = win32gui.GetWindowRect(hwnd)
            w_p, h_p = (rect[2] - rect[0]) - 1920, (rect[3] - rect[1]) - 1080
            p_left = rect[0] + w_p
            p_top = rect[1] + h_p
            set_window_activate(hwnd)
            if cbb_address=="手动录制":
                kkk = self.处理异常情况(hwnd, 0, 0, 1920, 1080, p_left, p_top)
                if kkk == 0:
                    logger.info("在意外界面 超时!")
                    time.sleep(3)
                    state.状态_需重新传送 = False
                    state.状态_已经有寻路了 = False
                    return False
                elif kkk == -1:
                    logger.info("全军覆没了!")
                    time.sleep(3)
                    state.状态_需重新传送 = True
                    state.状态_已经有寻路了 = False
                    return False
            # 临时发送消息给指定客户端
            message = f"解析脚本#@@#{jiaoben}"

            tangbaowss.send_msg(message)
            time.sleep(2)
            state.状态_是否回放中 = True
            tangbaowss.send_msg( "脚本执行#@@#1")
            while True:
                try:
                    time.sleep(0.1)
                    if state.状态_是否回放中 == False:
                        state.状态_需重新传送 = False
                        return True

                    if state.状态_循环开关 == False:
                        state.状态_需重新传送 = False
                        tangbaowss.send_msg( "是否回放#@@#假")
                        return False
                    if get_window_handle_at_mouse_position() != hwnd:
                        # 激活hwnd
                        hwnd = win32gui.FindWindow("UnityWndClass", state.GAME_TITLE)  # 替换成你实际的窗口句柄
                        set_window_activate(hwnd)
                        time.sleep(0.2)
                except:
                    time.sleep(0.5)
        except:
            logger.error(traceback.format_exc())
            state.状态_需重新传送 = False
            tangbaowss.send_msg( "是否回放#@@#假")
            return False
    def run_jiaoben(self,jiaoben,is_auto_f):

        hwnd = win32gui.FindWindow("UnityWndClass", state.GAME_TITLE)  # 替换成你实际的窗口句柄
        rect = win32gui.GetWindowRect(hwnd)
        w_p, h_p = (rect[2] - rect[0]) - 1920, (rect[3] - rect[1]) - 1080
        # F交互框位置
        left_dh, top_dh, right_dh, bottom_dh = [1161, 508, 1319, 572]  # 替换成你实际的区域坐标

        p_left = rect[0] + w_p
        p_top = rect[1] + h_p
        set_window_activate(hwnd)

        if state.状态_循环开关 == False:
            logger.info("强制退出!")
            state.状态_已经有寻路了 = False
            return False
        message = f"解析脚本#@@#{jiaoben}"
        tangbaowss.send_msg(message)
        time.sleep(2)
        tangbaowss.send_msg( "脚本执行#@@#1")
        state.状态_是否回放中 = True
        try:
            while True:

                try:
                    time.sleep(0.1)
                    if state.状态_是否回放中 == False:
                        state.状态_需重新传送 = False
                        state.状态_已经有寻路了 = False
                        logger.info("脚本运行完毕!")
                        return True
                    if state.状态_循环开关 == False:
                        state.状态_需重新传送 = False
                        state.状态_已经有寻路了 = False
                        tangbaowss.send_msg( "是否回放#@@#假")
                        logger.info("强制退出!")
                        return False
                    if get_window_handle_at_mouse_position() != hwnd:
                        # 激活hwnd
                        hwnd = win32gui.FindWindow("UnityWndClass", state.GAME_TITLE)  # 替换成你实际的窗口句柄
                        set_window_activate(hwnd)
                        time.sleep(0.2)
                    if is_auto_f:
                        if self.find_f(hwnd, 1050, 424, 1187, 648, p_left, p_top):
                            time.sleep(0.2)
                            if not self.find_duihuahuopengren(hwnd, left_dh, top_dh, right_dh, bottom_dh, p_left,
                                                              p_top) or self.find_pianpianhua(hwnd, left_dh, top_dh,
                                                                                              right_dh, bottom_dh, p_left,
                                                                                              p_top):
                                logger.info(f"顺手捡东西!")
                                state.状态_有目标 = True
                                pyautogui.keyDown('f')
                                time.sleep(0.1)
                                pyautogui.keyUp('f')
                                time.sleep(0.1)
                                pyautogui.keyDown('f')
                                time.sleep(0.1)
                                pyautogui.keyUp('f')



                except:
                    time.sleep(0.5)

        except:
            logger.error(traceback.format_exc())
            state.状态_需重新传送 = False
            state.状态_已经有寻路了 = False
            tangbaowss.send_msg( "是否回放#@@#假")
            return False
    def run_jixing(self):
        '''
        领取纪行奖励
        :return:
        '''

        global new_msg
        hwnd = win32gui.FindWindow("UnityWndClass", state.GAME_TITLE)  # 替换成你实际的窗口句柄
        rect = win32gui.GetWindowRect(hwnd)
        w_p, h_p = (rect[2] - rect[0]) - 1920, (rect[3] - rect[1]) - 1080
        # 设定截图区域的左上角坐标 (x, y) 和右下角坐标 (x, y)
        left, top, right, bottom = 0, 0, 1920, 1080  # 替换成你实际的区域坐标

        p_left = rect[0] + w_p
        p_top = rect[1] + h_p
        hwnd = win32gui.FindWindow("UnityWndClass", state.GAME_TITLE)  # 替换成你实际的窗口句柄
        set_window_activate(hwnd)
        time.sleep(0.2)
        paimeng_img = cv2.imdecode(np.fromfile(file="./datas/派蒙.png", dtype=np.uint8), cv2.IMREAD_UNCHANGED)
        big_img = screenshot(hwnd, left, top, right, bottom, None)
        if template_matching(big_img, paimeng_img, mask=None) == []:
            pyautogui.keyDown("esc")
            time.sleep(0.1)
            pyautogui.keyUp("esc")

            time.sleep(2)
        pyautogui.keyDown('alt')
        if not self.click_jixing(hwnd, left, top, right, bottom, p_left, p_top):  logger.info(
            "没有纪行要领取");pyautogui.keyUp('alt');return
        time.sleep(1)
        pyautogui.keyUp('alt')
        time.sleep(2)
        mouse_move(p_left + 962, p_top + 52)
        time.sleep(0.2)
        mouse_left_down()
        time.sleep(0.2)
        mouse_left_up()
        if not self.click_jixing_yijianlingqu(hwnd, left, top, right, bottom, p_left, p_top):  logger.info(
            "领取纪行任务经验失败");return
        time.sleep(1)
        mouse_move(p_left + 863, p_top + 55)
        time.sleep(0.2)
        mouse_left_down()
        time.sleep(0.2)
        mouse_left_up()
        time.sleep(1)
        mouse_left_down()
        time.sleep(0.2)
        mouse_left_up()
        time.sleep(1)
        if not self.click_jixing_yijianlingqu(hwnd, left, top, right, bottom, p_left, p_top):  logger.info(
            "没有奖励能领取");return
        time.sleep(1)
        mouse_left_down()
        time.sleep(0.2)
        mouse_left_up()
        time.sleep(1)
        pyautogui.keyDown("esc")
        time.sleep(0.1)
        pyautogui.keyUp("esc")
    def run_huanjue(self,path_list=[],zhanchagn="1"):
        global new_msg
        state.状态_检测中 = False
        hwnd = win32gui.FindWindow("UnityWndClass", state.GAME_TITLE)  # 替换成你实际的窗口句柄
        rect = win32gui.GetWindowRect(hwnd)
        w_p, h_p = (rect[2] - rect[0]) - 1920, (rect[3] - rect[1]) - 1080
        # 设定截图区域的左上角坐标 (x, y) 和右下角坐标 (x, y)
        left, top, right, bottom = 0, 0, 1920, 1080  # 替换成你实际的区域坐标

        p_left = rect[0] + w_p
        p_top = rect[1] + h_p
        hwnd = win32gui.FindWindow("UnityWndClass", state.GAME_TITLE)  # 替换成你实际的窗口句柄
        set_window_activate(hwnd)
        time.sleep(0.2)
        kkk = self.处理异常情况(hwnd, left, top, right, bottom, p_left, p_top)
        if kkk == 0:
            logger.info("在意外界面 超时!")
            time.sleep(3)
            state.状态_检测中 = False
            state.状态_已经有寻路了=False
            state.状态_需重新传送 = False
            return False
        elif kkk == -1:
            logger.info("全军覆没了!")
            time.sleep(3)
            state.状态_检测中 = False
            state.状态_已经有寻路了 = False
            state.状态_需重新传送 = True
            return False
        #按了后看有没有打开成功
        n=0
        while True:

            if n==0:
                kkk = self.处理异常情况(hwnd, left, top, right, bottom, p_left, p_top)
                if kkk == 0:
                    logger.info("在意外界面 超时!")
                    time.sleep(3)
                    state.状态_检测中 = False
                    state.状态_已经有寻路了 = False
                    state.状态_需重新传送 = False
                    return False
                elif kkk == -1:
                    logger.info("全军覆没了!")
                    time.sleep(3)
                    state.状态_检测中 = False
                    state.状态_已经有寻路了 = False
                    state.状态_需重新传送 = True
                    return False
                pyautogui.keyDown("x")
                time.sleep(0.2)
                pyautogui.keyUp("x")
                pyautogui.keyDown("l")
                time.sleep(0.2)
                pyautogui.keyUp("l")
                time.sleep(2)

            elif n>=20:
                n = -1
            n += 1
            time.sleep(1)
            if state.状态_循环开关 == False:
                state.状态_需重新传送 = False
                state.状态_已经有寻路了 = False
                tangbaowss.send_msg( "是否回放#@@#假")
                logger.info("强制退出!")
                return False
            if get_window_handle_at_mouse_position() != hwnd:
                # 激活hwnd
                hwnd = win32gui.FindWindow("UnityWndClass", state.GAME_TITLE)  # 替换成你实际的窗口句柄
                set_window_activate(hwnd)
                time.sleep(0.2)

            if state.状态_全局暂停:
                tangbaowss.send_msg( "是否回放#@@#假")
                time.sleep(2)
                continue
            if self.click_kuaisubiandui(hwnd, left, top, right, bottom , p_left, p_top):
                time.sleep(2)
                break


        #加载角色图片数据
        img_datas=[]
        point_list=[[96,189],
                    [255,198],
                    [404,200],
                    [568,190],
                    ]
        for item in path_list:
            img_datas.append( cv2.imdecode(np.fromfile(file=item, dtype=np.uint8),cv2.IMREAD_UNCHANGED))
        #将历史角色清空掉
        for point in point_list:
            #点击对应坑位
            mouse_move(p_left +point[0],p_top +point[1])
            time.sleep(0.2)
            mouse_left_down()
            time.sleep(0.2)
            mouse_left_up()
            time.sleep(0.2)
            if not self.find_baocunpeizhi(hwnd, left, top, right, bottom, p_left, p_top):
                break
        logger.info("清空角色成功!")
        # 循环选择角色
        a = 0
        for i,img in enumerate(img_datas):
            ret =False

            while not ret:

                logger.info(str(a))
                if state.状态_循环开关 == False:
                    state.状态_需重新传送 = False
                    state.状态_已经有寻路了 = False
                    tangbaowss.send_msg( "是否回放#@@#假")
                    logger.info("强制退出!")
                    return False
                if get_window_handle_at_mouse_position() != hwnd:
                    # 激活hwnd
                    hwnd = win32gui.FindWindow("UnityWndClass", state.GAME_TITLE)  # 替换成你实际的窗口句柄
                    set_window_activate(hwnd)
                    time.sleep(0.2)

                if state.状态_全局暂停:
                    tangbaowss.send_msg( "是否回放#@@#假")
                    time.sleep(2)
                    continue

                ret= find_img_and_click(img, hwnd, left, top, right, bottom, p_left, p_top, f"勾选 角色{i+1}", 1,
                                               1,
                                               threshold=0.76)
                if ret:
                    time.sleep(1)
                    break

                if 5<a<11:
                    pyautogui.moveTo(p_left +342,p_top +415)
                    pyautogui.dragTo(p_left +346,p_top +833, duration=0.5, tween=pyautogui.easeOutSine)

                else:
                    pyautogui.moveTo(p_left +346, p_top +833)
                    pyautogui.dragTo(p_left +342, p_top +415, duration=0.5, tween=pyautogui.easeOutSine)
                a+=1
                if a==11:
                    a=0
                time.sleep(1)




        #保存配置
        mouse_move(p_left +453,p_top +1016)
        time.sleep(0.2)
        mouse_left_down()
        time.sleep(0.2)
        mouse_left_up()

        time.sleep(5)
        if state.状态_循环开关 == False:
            state.状态_需重新传送 = False
            state.状态_已经有寻路了 = False
            tangbaowss.send_msg( "是否回放#@@#假")
            logger.info("强制退出!")
            return False
        pyautogui.keyDown("esc")
        time.sleep(0.1)
        pyautogui.keyUp("esc")
        time.sleep(2)
        pyautogui.keyDown(zhanchagn)
        time.sleep(0.1)
        pyautogui.keyUp(zhanchagn)
        time.sleep(2)
        state.状态_需重新传送 = False
        state.状态_已经有寻路了 = False
        return True
    def 处理异常情况(self,hwnd,left,top,right,bottom,p_left,p_top):
        '''

        :param hwnd:
        :param left:
        :param top:
        :param right:
        :param bottom:
        :param p_left:
        :param p_top:
        :return:
        '''
        paimeng_img = cv2.imdecode(np.fromfile(file="./datas/派蒙.png", dtype=np.uint8), cv2.IMREAD_UNCHANGED)
        # 判断是否卡在了对话界面
        for nnn in range(120):
            if state.状态_循环开关 == False:
                logger.info("强制退出!")
                pyautogui.keyUp('w')
                state.状态_已经有寻路了 = False
                state.状态_是否回放中 = False
                state.状态_检测中=False
                tangbaowss.send_msg( "是否回放#@@#假")
                state.状态_需重新传送 = False
                return 1
            big_img = screenshot(hwnd, left, top, right, bottom, None)
            rrrr = template_matching(big_img, paimeng_img, mask=None)
            if rrrr == []:
                rdd = random.randint(1, 4)
                if rdd == 1:
                    pyautogui.keyDown("x")
                    time.sleep(0.2)
                    pyautogui.keyUp("x")
                    mouse_move(p_left + 1397, p_top + 804)
                    time.sleep(0.2)
                    mouse_left_down()
                    time.sleep(0.2)
                    mouse_left_up()
                    time.sleep(0.2)
                elif rdd == 2:

                    pyautogui.keyDown("esc")
                    time.sleep(0.1)
                    pyautogui.keyUp("esc")
                    time.sleep(1)
                else:

                    pyautogui.keyDown('space')
                    time.sleep(0.1)
                    pyautogui.keyUp('space')
                logger.info("未知界面 按esc 和 点击对话! " + str(nnn))

            # 判断是否死亡
            ret_sw = self.find_zhuyidiren(hwnd, left, top, right, bottom, p_left, p_top)
            if ret_sw:
                state.状态_检测中 = False
                return -1
            else:
                if rrrr != []:

                    return 1
            time.sleep(1)

        return 0
    def find_f_keep_going(self, hwnd, left, top, right, bottom, p_left, p_top, isFanxiang):
        img = cv2.imdecode(np.fromfile(file="./datas/F.png", dtype=np.uint8), cv2.IMREAD_UNCHANGED)
        if isFanxiang:
            keepKey = 's'
        else:
            keepKey = 'w'
        if find_img_and_click(img, hwnd, left, top, right, bottom, p_left, p_top, "点击 F", 1000, 0.05, False,
                                   keepKey=keepKey):
            if isFanxiang:
                pyautogui.keyUp('s')
            else:
                pyautogui.keyUp('w')
            pyautogui.keyDown('f')
            time.sleep(0.2)
            pyautogui.keyUp('f')
            time.sleep(0.3)
            pyautogui.keyDown('f')
            time.sleep(0.2)
            pyautogui.keyUp('f')

            return True
        else:
            return False
    def click_danrentiaozhan(self, hwnd, left, top, right, bottom, p_left, p_top):
        img = cv2.imdecode(np.fromfile(file="./datas/单人挑战.png", dtype=np.uint8), cv2.IMREAD_UNCHANGED)
        return find_img_and_click(img, hwnd, left, top, right, bottom, p_left, p_top, "点击 单人挑战")
    def click_dimaiyichang(self, hwnd, left, top, right, bottom, p_left, p_top):
        img = cv2.imdecode(np.fromfile(file="./datas/地脉异常.png", dtype=np.uint8), cv2.IMREAD_UNCHANGED)
        return find_img_and_click(img, hwnd, left, top, right, bottom, p_left, p_top, "点击 地脉异常", 1, 0.01,
                                       threshold=0.76)
    def click_jixing(self, hwnd, left, top, right, bottom, p_left, p_top):
        img = cv2.imdecode(np.fromfile(file="./datas/纪行.png", dtype=np.uint8), cv2.IMREAD_UNCHANGED)
        mask = img[:, :, 3]  # 提取透明度通道作为掩码
        return find_img_and_click(img, hwnd, left, top, right, bottom, p_left, p_top, "点击 纪行", 10, 1,
                                       threshold=0.76, mask=mask, double_click=True)
    def click_jixing_renwu(self, hwnd, left, top, right, bottom, p_left, p_top):
        img = cv2.imdecode(np.fromfile(file="./datas/纪行_任务.png", dtype=np.uint8), cv2.IMREAD_UNCHANGED)
        mask = img[:, :, 3]  # 提取透明度通道作为掩码
        return find_img_and_click(img, hwnd, left, top, right, bottom, p_left, p_top, "点击 纪行_任务", 10, 1,
                                       threshold=0.6, mask=mask)
    def click_jixing_yijianlingqu(self, hwnd, left, top, right, bottom, p_left, p_top):
        img = cv2.imdecode(np.fromfile(file="./datas/纪行_一键领取.png", dtype=np.uint8), cv2.IMREAD_UNCHANGED)
        return find_img_and_click(img, hwnd, left, top, right, bottom, p_left, p_top, "点击 纪行_一键领取", 10, 1,
                                       threshold=0.76)
    def click_kuaisubiandui(self, hwnd, left, top, right, bottom, p_left, p_top):
        img = cv2.imdecode(np.fromfile(file="./datas/快速编队.png", dtype=np.uint8), cv2.IMREAD_UNCHANGED)
        return find_img_and_click(img, hwnd, left, top, right, bottom, p_left, p_top, "点击 快速编队", 1, 1,
                                       threshold=0.76)
    def find_baocunpeizhi(self, hwnd, left, top, right, bottom, p_left, p_top):
        img = cv2.imdecode(np.fromfile(file="./datas/保存配置.png", dtype=np.uint8), cv2.IMREAD_UNCHANGED)
        return find_img_and_click(img, hwnd, left, top, right, bottom, p_left, p_top, "查找 保存配置", 1, 1,
                                       threshold=0.76,is_click=False)
    def click_jixing_jixing(self, hwnd, left, top, right, bottom, p_left, p_top):
        img = cv2.imdecode(np.fromfile(file="./datas/纪行_纪行.png", dtype=np.uint8), cv2.IMREAD_UNCHANGED)
        mask = img[:, :, 3]  # 提取透明度通道作为掩码
        return find_img_and_click(img, hwnd, left, top, right, bottom, p_left, p_top, "点击 纪行_纪行", 10, 1,
                                       threshold=0.76, mask=mask)
    def click_kaishitiaozhan(self, hwnd, left, top, right, bottom, p_left, p_top):
        img = cv2.imdecode(np.fromfile(file="./datas/开始挑战.png", dtype=np.uint8), cv2.IMREAD_UNCHANGED)
        return find_img_and_click(img, hwnd, left, top, right, bottom, p_left, p_top, "点击 开始挑战")
    def find_shuzhibugou(self, hwnd, left, top, right, bottom, p_left, p_top):
        img = cv2.imdecode(np.fromfile(file="./datas/树脂不够.png", dtype=np.uint8), cv2.IMREAD_UNCHANGED)
        mask = img[:, :, 3]  # 提取透明度通道作为掩码
        return find_img_and_click(img, hwnd, left, top, right, bottom, p_left, p_top, "点击 树脂不够", 3,
                                       threshold=0.65, is_click=False, mask=mask)
    def find_shuzhibugou2(self, hwnd, left, top, right, bottom, p_left, p_top):
        img = cv2.imdecode(np.fromfile(file="./datas/收取完成秘境.png", dtype=np.uint8), cv2.IMREAD_UNCHANGED)
        mask = img[:, :, 3]  # 提取透明度通道作为掩码
        return find_img_and_click(img, hwnd, left, top, right, bottom, p_left, p_top, "发现 树脂不够", 3,
                                       threshold=0.65, is_click=False, mask=mask)
    def find_zidongtuichu(self, hwnd, left, top, right, bottom, p_left, p_top):

        img = cv2.imdecode(np.fromfile(file="./datas/自动退出.png", dtype=np.uint8), cv2.IMREAD_UNCHANGED)
        mask = img[:, :, 3]  # 提取透明度通道作为掩码
        return find_img_and_click(img, hwnd, left, top, right, bottom, p_left, p_top, "发现 自动退出", 1,
                                       threshold=0.65, is_click=False, mask=mask,step=0.001)
    def find_jixutiaozhan(self, hwnd, left, top, right, bottom, p_left, p_top):
        img = cv2.imdecode(np.fromfile(file="./datas/继续挑战.png", dtype=np.uint8), cv2.IMREAD_UNCHANGED)
        mask = None  # 提取透明度通道作为掩码
        return find_img_and_click(img, hwnd, left, top, right, bottom, p_left, p_top, "发现 继续挑战", 1,
                                       threshold=0.65, is_click=False, mask=mask,step=0.001)
    def find_f(self, hwnd, left, top, right, bottom, p_left, p_top):
        img = cv2.imdecode(np.fromfile(file="./datas/f.png", dtype=np.uint8), cv2.IMREAD_UNCHANGED)
        mask =None# img[:, :, 3]  # 提取透明度通道作为掩码
        return find_img_and_click(img, hwnd, left, top, right, bottom, p_left, p_top, "寻找 F", 1,step=0.001,
                                       threshold=0.75, is_click=False, mask=mask)
    def find_zhuyidiren(self, hwnd, left, top, right, bottom, p_left, p_top):
        img = cv2.imdecode(np.fromfile(file="./datas/注意敌人.png", dtype=np.uint8), cv2.IMREAD_UNCHANGED)
        return find_img_and_click(img, hwnd, left, top, right, bottom, p_left, p_top, "点击 注意敌人", 2,
                                       step=0.01,
                                       threshold=0.85, is_click=True)
    def find_duihuahuopengren(self, hwnd, left, top, right, bottom, p_left, p_top):
        global imgs_duihuahuopengren
        return find_imgs_and_click(imgs_duihuahuopengren, hwnd, left, top, right, bottom, p_left, p_top, "发现 对话或烹饪", 1,
                                        step=0.001, is_click=False)
    def find_pianpianhua(self, hwnd, left, top, right, bottom, p_left, p_top):
        global imgs_pianpianhua
        return find_imgs_and_click(imgs_pianpianhua, hwnd, left, top, right, bottom, p_left, p_top, "发现 骗骗花", 1,
                                        step=0.001, is_click=False)
    def distance_to_target(self, d):

        h = float(d["points"][1][0] - d["points"][0][0])
        w = float(d["points"][1][1] - d["points"][0][1])
        # # # 计算距离的平方，这里使用欧几里得距离的平方作为衡量标准
        dist = (d["x"] - self.centre_point[0]) ** 2 + (d["y"] - self.centre_point[1]) ** 2
        angle = get_angle(self.centre_point, [d["x"], d["y"]])
        if angle >= 300 or angle <= 60:
            dist *= 999
        if w < 60:
            dist *= 0.1
        return dist + w * h


if __name__ == '__main__':

    while True:
        time.sleep(0.0001)
        pass
