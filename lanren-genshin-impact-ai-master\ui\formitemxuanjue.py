# -*- coding: utf-8 -*-

# Form implementation generated from reading ui file 'formitemxuanjue.ui'
#
# Created by: PyQt5 UI code generator 5.15.7
#
# WARNING: Any manual changes made to this file will be lost when pyuic5 is
# run again.  Do not edit this file unless you know what you are doing.


from PyQt5 import QtCore, QtGui, QtWidgets


class Ui_formitemxuanjue(object):
    def setupUi(self, formitemxuanjue):
        formitemxuanjue.setObjectName("formitemxuanjue")
        formitemxuanjue.resize(286, 172)
        self.gridLayout = QtWidgets.QGridLayout(formitemxuanjue)
        self.gridLayout.setObjectName("gridLayout")
        self.horizontalLayout = QtWidgets.QHBoxLayout()
        self.horizontalLayout.setObjectName("horizontalLayout")
        self.led_name = QtWidgets.QLineEdit(formitemxuanjue)
        self.led_name.setMinimumSize(QtCore.QSize(0, 0))
        self.led_name.setMaximumSize(QtCore.QSize(999, 9999))
        self.led_name.setStyleSheet("color: rgb(255, 255, 255);")
        self.led_name.setAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignLeft|QtCore.Qt.AlignVCenter)
        self.led_name.setObjectName("led_name")
        self.horizontalLayout.addWidget(self.led_name)
        self.cb_is_checked = QtWidgets.QCheckBox(formitemxuanjue)
        self.cb_is_checked.setMinimumSize(QtCore.QSize(0, 0))
        self.cb_is_checked.setMaximumSize(QtCore.QSize(999999, 9999999))
        self.cb_is_checked.setLayoutDirection(QtCore.Qt.RightToLeft)
        self.cb_is_checked.setStyleSheet("")
        self.cb_is_checked.setText("")
        self.cb_is_checked.setChecked(True)
        self.cb_is_checked.setObjectName("cb_is_checked")
        self.horizontalLayout.addWidget(self.cb_is_checked)
        self.gridLayout.addLayout(self.horizontalLayout, 0, 0, 1, 4)
        self.lb_zhanchang = QtWidgets.QLabel(formitemxuanjue)
        self.lb_zhanchang.setMinimumSize(QtCore.QSize(50, 50))
        self.lb_zhanchang.setAlignment(QtCore.Qt.AlignCenter)
        self.lb_zhanchang.setObjectName("lb_zhanchang")
        self.gridLayout.addWidget(self.lb_zhanchang, 1, 0, 1, 1)
        self.led_main = QtWidgets.QLineEdit(formitemxuanjue)
        self.led_main.setMinimumSize(QtCore.QSize(50, 50))
        self.led_main.setMaximumSize(QtCore.QSize(50, 50))
        font = QtGui.QFont()
        font.setFamily("ADMUI3Lg")
        font.setPointSize(14)
        self.led_main.setFont(font)
        self.led_main.setStyleSheet("color: rgb(255, 255, 255);")
        self.led_main.setAlignment(QtCore.Qt.AlignCenter)
        self.led_main.setObjectName("led_main")
        self.gridLayout.addWidget(self.led_main, 1, 1, 1, 1)
        self.cbb_1 = QtWidgets.QComboBox(formitemxuanjue)
        self.cbb_1.setMinimumSize(QtCore.QSize(50, 50))
        self.cbb_1.setMaximumSize(QtCore.QSize(9999, 9999))
        self.cbb_1.setStyleSheet("color: rgb(255, 255, 255);\n"
"margin: 0px; \n"
"padding: 0px;\n"
"")
        self.cbb_1.setEditable(False)
        self.cbb_1.setInsertPolicy(QtWidgets.QComboBox.NoInsert)
        self.cbb_1.setDuplicatesEnabled(False)
        self.cbb_1.setFrame(True)
        self.cbb_1.setObjectName("cbb_1")
        self.gridLayout.addWidget(self.cbb_1, 1, 2, 1, 1)
        self.bt_start = QtWidgets.QPushButton(formitemxuanjue)
        self.bt_start.setMinimumSize(QtCore.QSize(50, 50))
        self.bt_start.setMaximumSize(QtCore.QSize(999999, 999999))
        self.bt_start.setObjectName("bt_start")
        self.gridLayout.addWidget(self.bt_start, 1, 3, 1, 1)
        self.cbb_2 = QtWidgets.QComboBox(formitemxuanjue)
        self.cbb_2.setMinimumSize(QtCore.QSize(50, 50))
        self.cbb_2.setMaximumSize(QtCore.QSize(9999, 9999))
        self.cbb_2.setStyleSheet("color: rgb(255, 255, 255);\n"
"margin: 0px; \n"
"padding: 0px;")
        self.cbb_2.setEditable(False)
        self.cbb_2.setInsertPolicy(QtWidgets.QComboBox.NoInsert)
        self.cbb_2.setDuplicatesEnabled(False)
        self.cbb_2.setFrame(True)
        self.cbb_2.setObjectName("cbb_2")
        self.gridLayout.addWidget(self.cbb_2, 2, 0, 1, 1)
        self.cbb_3 = QtWidgets.QComboBox(formitemxuanjue)
        self.cbb_3.setMinimumSize(QtCore.QSize(50, 50))
        self.cbb_3.setMaximumSize(QtCore.QSize(9999, 9999))
        self.cbb_3.setStyleSheet("color: rgb(255, 255, 255);\n"
"margin: 0px; \n"
"padding: 0px;")
        self.cbb_3.setEditable(False)
        self.cbb_3.setInsertPolicy(QtWidgets.QComboBox.NoInsert)
        self.cbb_3.setDuplicatesEnabled(False)
        self.cbb_3.setFrame(True)
        self.cbb_3.setObjectName("cbb_3")
        self.gridLayout.addWidget(self.cbb_3, 2, 1, 1, 1)
        self.cbb_4 = QtWidgets.QComboBox(formitemxuanjue)
        self.cbb_4.setMinimumSize(QtCore.QSize(50, 50))
        self.cbb_4.setMaximumSize(QtCore.QSize(9999, 9999))
        self.cbb_4.setStyleSheet("color: rgb(255, 255, 255);\n"
"margin: 0px; \n"
"padding: 0px;")
        self.cbb_4.setEditable(False)
        self.cbb_4.setInsertPolicy(QtWidgets.QComboBox.NoInsert)
        self.cbb_4.setDuplicatesEnabled(False)
        self.cbb_4.setFrame(True)
        self.cbb_4.setObjectName("cbb_4")
        self.gridLayout.addWidget(self.cbb_4, 2, 2, 1, 1)
        self.bt_del = QtWidgets.QPushButton(formitemxuanjue)
        self.bt_del.setMinimumSize(QtCore.QSize(50, 50))
        self.bt_del.setMaximumSize(QtCore.QSize(999999, 999999))
        self.bt_del.setObjectName("bt_del")
        self.gridLayout.addWidget(self.bt_del, 2, 3, 1, 1)

        self.retranslateUi(formitemxuanjue)
        QtCore.QMetaObject.connectSlotsByName(formitemxuanjue)

    def retranslateUi(self, formitemxuanjue):
        _translate = QtCore.QCoreApplication.translate
        formitemxuanjue.setWindowTitle(_translate("formitemxuanjue", "Form"))
        self.led_name.setText(_translate("formitemxuanjue", "蒙德锄地"))
        self.lb_zhanchang.setText(_translate("formitemxuanjue", "站场:"))
        self.led_main.setText(_translate("formitemxuanjue", "4"))
        self.bt_start.setText(_translate("formitemxuanjue", "启动"))
        self.bt_del.setText(_translate("formitemxuanjue", "删除\n"
"任务"))
