# -*- coding: utf-8 -*-
import os
import cv2
import numpy as np
# pip install opencv-contrib-python
class  StitchImages:

    def __init__(self,):
        '''
        全景图合成类
        '''
        pass
    def stitch_images(self,image_paths,out_path="",isShow=False):
        # 创建 Stitcher 对象
        stitcher = cv2.Stitcher_create()
        images = [cv2.imdecode(np.fromfile(file=path, dtype=np.uint8), cv2.IMREAD_COLOR) for path in image_paths]
        # 调用 Stitcher 进行拼接
        status, stitched_image = stitcher.stitch(images)
        # 检查拼接是否成功
        if status == cv2.Stitcher_OK:
            # 保存拼接后的全景图
            cv2.imwrite(out_path, stitched_image)

            print('全景图拼接成功，并已保存为 '+out_path)

        else:
            print('全景图拼接失败')
            return False
        if isShow:
            cv2.imshow('Panorama', stitched_image)
            cv2.waitKey(0)
            cv2.destroyAllWindows()
        return True
    def get_image_paths(self,dir="./"):
        image_paths=[]
        #遍历一个文件夹 ['.png','.jpg','.bmp']找出符合这个的文件加入到image_paths中
        for root, dirs, files in os.walk(dir):
            for file in files:
                if file.endswith('.png') or file.endswith('.jpg') or file.endswith('.bmp'):
                    image_paths.append(os.path.join(root, file))
        return image_paths
if __name__ == '__main__':
    si=StitchImages()
    image_paths=si.get_image_paths("./datas/img")
    si.stitch_images(image_paths,"./datas/max_map/max_map.png")

