# -*- coding: utf-8 -*-
import ctypes
import math
import time
from ctypes import wintypes

import psutil
import pyautogui
import win32api
import win32con
import win32gui
# 获取当前屏幕宽度和高度
screen_width = ctypes.windll.user32.GetSystemMetrics(0)
screen_height = ctypes.windll.user32.GetSystemMetrics(1)
# 定义MOUSEINPUT结构体
class MOUSEINPUT(ctypes.Structure):
    _fields_ = [("dx", ctypes.c_long),
                ("dy", ctypes.c_long),
                ("mouseData", ctypes.c_ulong),
                ("dwFlags", ctypes.c_ulong),
                ("time", ctypes.c_ulong),
                ("dwExtraInfo", ctypes.POINTER(ctypes.c_ulong))]
# 定义KEYBDINPUT结构体
class KEYBDINPUT(ctypes.Structure):
    _fields_ = [("wVk", ctypes.wintypes.WORD),
                ("wScan", ctypes.wintypes.WORD),
                ("dwFlags", ctypes.c_ulong),
                ("time", ctypes.c_ulong),
                ("dwExtraInfo", ctypes.POINTER(ctypes.c_ulong))]
# 定义INPUT结构体
class INPUT(ctypes.Structure):
    class _INPUT(ctypes.Union):
        _fields_ =[("mi", MOUSEINPUT),
                    ("ki", KEYBDINPUT)]

    _fields_ = [("type", ctypes.c_ulong),
                ("input", _INPUT)]
# 鼠标相对移动函数
def mouse_moveR(dx, dy):
    # 构造输入事件列表
    inputs = []
    inputs.append(INPUT(type=0, input=INPUT._INPUT(
        mi=MOUSEINPUT(dx=dx, dy=dy, mouseData=0, dwFlags=win32con.MOUSEEVENTF_MOVE, time=0, dwExtraInfo=None))))

    # 发送输入事件
    ctypes.windll.user32.SendInput(len(inputs), ctypes.byref(inputs[0]), ctypes.sizeof(INPUT))
def mouse_move(x, y):
    # 计算目标位置的绝对坐标值
    absolute_x = int(x / screen_width * 65535)
    absolute_y = int(y / screen_height * 65535)
    # 构造输入事件列表
    inputs = []
    inputs.append(INPUT(type=0, input=INPUT._INPUT(
        mi=MOUSEINPUT(dx=absolute_x, dy=absolute_y, mouseData=0, dwFlags=win32con.MOUSEEVENTF_MOVE | win32con.MOUSEEVENTF_ABSOLUTE,
                      time=0, dwExtraInfo=None))))
    # 发送输入事件
    ctypes.windll.user32.SendInput(len(inputs), ctypes.byref(inputs[0]), ctypes.sizeof(INPUT))
# 鼠标左键按下
def mouse_left_down():
    # 构造输入事件列表
    inputs = []
    inputs.append(INPUT(type=0, input=INPUT._INPUT(
        mi=MOUSEINPUT(dx=0, dy=0, mouseData=0, dwFlags=win32con.MOUSEEVENTF_LEFTDOWN, time=0, dwExtraInfo=None))))

    # 发送输入事件
    ctypes.windll.user32.SendInput(len(inputs), ctypes.byref(inputs[0]), ctypes.sizeof(INPUT))
# 鼠标左键弹起
def mouse_left_up():
    # 构造输入事件列表
    inputs = []
    inputs.append(INPUT(type=0, input=INPUT._INPUT(
        mi=MOUSEINPUT(dx=0, dy=0, mouseData=0, dwFlags=win32con.MOUSEEVENTF_LEFTUP, time=0, dwExtraInfo=None))))

    # 发送输入事件
    ctypes.windll.user32.SendInput(len(inputs), ctypes.byref(inputs[0]), ctypes.sizeof(INPUT))
# 鼠标右键按下
def mouse_right_down():
    # 构造输入事件列表
    inputs = []
    inputs.append(INPUT(type=0, input=INPUT._INPUT(
        mi=MOUSEINPUT(dx=0, dy=0, mouseData=0, dwFlags=win32con.MOUSEEVENTF_RIGHTDOWN, time=0, dwExtraInfo=None))))

    # 发送输入事件
    ctypes.windll.user32.SendInput(len(inputs), ctypes.byref(inputs[0]), ctypes.sizeof(INPUT))

# 鼠标右键弹起
def mouse_right_up():
    # 构造输入事件列表
    inputs = []
    inputs.append(INPUT(type=0, input=INPUT._INPUT(
        mi=MOUSEINPUT(dx=0, dy=0, mouseData=0, dwFlags=win32con.MOUSEEVENTF_RIGHTUP, time=0, dwExtraInfo=None))))

    # 发送输入事件
    ctypes.windll.user32.SendInput(len(inputs), ctypes.byref(inputs[0]), ctypes.sizeof(INPUT))
# 鼠标中键按下
def mouse_middle_down():
    # 构造输入事件列表
    inputs = []
    inputs.append(INPUT(type=0, input=INPUT._INPUT(
        mi=MOUSEINPUT(dx=0, dy=0, mouseData=0, dwFlags=win32con.MOUSEEVENTF_MIDDLEDOWN, time=0, dwExtraInfo=None))))

    # 发送输入事件
    ctypes.windll.user32.SendInput(len(inputs), ctypes.byref(inputs[0]), ctypes.sizeof(INPUT))
# 鼠标中键弹起
def mouse_middle_up():
    # 构造输入事件列表
    inputs = []
    inputs.append(INPUT(type=0, input=INPUT._INPUT(
        mi=MOUSEINPUT(dx=0, dy=0, mouseData=0, dwFlags=win32con.MOUSEEVENTF_MIDDLEUP, time=0, dwExtraInfo=None))))

    # 发送输入事件
    ctypes.windll.user32.SendInput(len(inputs), ctypes.byref(inputs[0]), ctypes.sizeof(INPUT))
original_style=0
def set_mouse_through(hwnd,setPenetrate=True):
    global original_style
    """设置窗口鼠标穿透"""
    if setPenetrate:
        style = win32gui.GetWindowLong(hwnd, win32con.GWL_EXSTYLE)
        original_style = style  # 保存原始样式，以便恢复
        new_style = style | win32con.WS_EX_TRANSPARENT | win32con.WS_EX_LAYERED
        win32gui.SetWindowLong(hwnd, win32con.GWL_EXSTYLE, new_style)
    else:
        if original_style==0:
            win32gui.SetWindowLong(hwnd, win32con.GWL_EXSTYLE,  win32con.WS_EX_LAYERED)
        else:
            win32gui.SetWindowLong(hwnd, win32con.GWL_EXSTYLE, original_style)
def set_window_activate(hwnd):
    """强制激活窗口"""
    try:
        #判断窗口是否为最小化
        if win32gui.IsIconic(hwnd):
            # 还原窗口
            win32gui.ShowWindow(hwnd, win32con.SW_RESTORE)

        # 将窗口置顶
        win32gui.SetWindowPos(hwnd, win32con.HWND_TOPMOST, 0, 0, 0, 0, win32con.SWP_NOMOVE | win32con.SWP_NOSIZE)
        # 取消置顶
        win32gui.SetWindowPos(hwnd, win32con.HWND_NOTOPMOST, 0, 0, 0, 0, win32con.SWP_NOMOVE | win32con.SWP_NOSIZE)

        win32gui.SetForegroundWindow(hwnd)
    except:
        pass
def set_window_display_affinity(hwnd):
    # WDA_NONE
    # 0x00000000
    # 对窗口的显示位置没有限制。
    # WDA_MONITOR
    # 0x00000001
    # 窗口内容仅显示在监视器上。 在其他任何位置，窗口都会显示，其中没有任何内容。
    # WDA_EXCLUDEFROMCAPTURE
    # 0x00000011
    # 窗口仅显示在监视器上。 在其他位置，窗口根本不显示。
    # 此相关性的一个用途是用于显示视频录制控件的窗口，以便控件不包含在捕获中。
    dwAffinity = wintypes.DWORD(0x00000011)  # 使用wintypes模块,“0x00000001”可换成其他值
    dll = ctypes.cdll.LoadLibrary("C:\\WINDOWS\\system32\\user32.dll")  # 接着导入user32.dll 模块
    dll.SetWindowDisplayAffinity(hwnd, dwAffinity)
# 键盘按下函数
def key_down(vk_code):
    inputs = []
    inputs.append(INPUT(type=1, input=INPUT._INPUT(
        ki=KEYBDINPUT(wVk=vk_code, wScan=0, dwFlags=0, time=0, dwExtraInfo=None))))

    # 发送输入事件
    ctypes.windll.user32.SendInput(len(inputs), ctypes.byref(inputs[0]), ctypes.sizeof(INPUT))
# 键盘弹起函数
def key_up(vk_code):
    inputs = []
    inputs.append(INPUT(type=1, input=INPUT._INPUT(
        ki=KEYBDINPUT(wVk=vk_code, wScan=0, dwFlags=win32con.KEYEVENTF_KEYUP, time=0, dwExtraInfo=None))))

    # 发送输入事件
    ctypes.windll.user32.SendInput(len(inputs), ctypes.byref(inputs[0]), ctypes.sizeof(INPUT))
# 键盘按下函数
def key_down2(vk_code):
    win32api.keybd_event(vk_code, 0, 0, 0)
# 键盘弹起函数
def key_up2(vk_code):
    win32api.keybd_event(vk_code, 0, win32con.KEYEVENTF_KEYUP, 0)
def get_windows_screen_scale():
    """ 调用 Windows API 函数获取缩放比例 """
    try:
        user32 = ctypes.windll.user32
        user32.SetProcessDPIAware()
        dpi = user32.GetDpiForSystem()
        # 计算缩放比例
        scale = round(dpi / 96.0, 2)

        return scale
    except Exception as e:
        print("获取缩放比例时出错:", e)
        return 1
def set_console_position(x, y, w,h):
    hwnd = ctypes.windll.kernel32.GetConsoleWindow()
    if hwnd:
        ctypes.windll.user32.MoveWindow(hwnd, x, y, w, h, True)
def get_window_handle_at_mouse_position():
    active_hwnd = ctypes.windll.user32.GetForegroundWindow()
    return active_hwnd
def find_child_window_handle(parent, class_name, window_title):
    hwnd_child = 0
    while True:
        hwnd_child = win32gui.FindWindowEx(parent, hwnd_child, class_name, None)
        if hwnd_child:

            buffer=win32gui.GetWindowText(hwnd_child)
            if window_title in buffer:
                return hwnd_child
        else:
            return 0
def get_windows_screen_scale():
    """ 调用 Windows API 函数获取缩放比例 """
    try:
        user32 = ctypes.windll.user32
        user32.SetProcessDPIAware()
        dpi = user32.GetDpiForSystem()
        # 计算缩放比例
        scale = round(dpi / 96.0, 2)

        return scale
    except Exception as e:
        print("获取缩放比例时出错:", e)
        return 1
def set_console_position(x, y, w,h):
    hwnd = ctypes.windll.kernel32.GetConsoleWindow()
    if hwnd:
        ctypes.windll.user32.MoveWindow(hwnd, x, y, w, h, True)
def set_angle(angle_now, angle_target, fault=6):
    '''
    模拟鼠标 控制镜头到指定方向角度
    Args:
        angle_now:
        angle_target:
        fault: 最少容差

    Returns:是否需要调整

    '''
    ret = True
    angle_diff = abs(angle_now - angle_target)
    if angle_diff <= fault:
        ret = False
    if ret == False:
        return False

    angle_diff = int((angle_target - angle_now) % 360)
    if angle_diff > 180:
        fangxiang = -1
        angle_diff = 360 - angle_diff
    else:
        fangxiang = 1
    if abs(angle_diff < fault):
        return True
    # print(f"调整视角 相差:{fangxiang * angle_diff}° 当前:{angle_now}° 目标:{angle_target}°  ")
    if angle_diff >= 100:
        mouse_moveR(int(fangxiang * 6 * angle_diff), 0)
    else:
        mouse_moveR(int(fangxiang * 5 * angle_diff), 0)
    return True
def get_angle(start_pos, next_pos):
    '''
    计算两个坐标之间的角度   相对以北方向为0度的角度
    Args:
        start_pos:
        next_pos:

    Returns:

    '''
    # 计算方向向量
    direction_x = next_pos[0] - start_pos[0]
    direction_y = next_pos[1] - start_pos[1]

    # 使用反正切函数计算弧度角度值
    angle_rad = math.atan2(direction_y, direction_x)

    # 将弧度角度值转换为以北方向为0度的角度
    angle_deg = math.degrees(angle_rad)
    # 将角度值转换为顺时针方向
    angle_deg = int((angle_deg + 360 + 90) % 360)

    return angle_deg
def running(bengpao=True):
    pyautogui.keyDown('w')
    if bengpao:
        mouse_right_down()
        time.sleep(1)
        mouse_right_up()
def speed_up():
    pyautogui.keyDown('w')
    mouse_right_down()
    time.sleep(0.2)
    mouse_right_up()

def show_hide_window(hwnd, show):
    if show:
        win32gui.ShowWindow(hwnd, 5)  # 5对应SW_SHOW
    else:
        win32gui.ShowWindow(hwnd, 0)  # 0对应SW_HIDE
def terminate_process_by_name(process_name):
    # 遍历所有当前运行的进程
    for proc in psutil.process_iter(attrs=['pid', 'name']):
        try:
            # 检查进程名称
            if proc.info['name'] == process_name:
                # 优雅地终止进程
                proc.terminate()  # 或者使用 proc.kill() 强制结束
                print(f"成功终止进程: {proc.info['name']} (PID: {proc.info['pid']})")
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            continue
