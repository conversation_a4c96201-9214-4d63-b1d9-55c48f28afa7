# -*- coding: utf-8 -*-
#opencv 必须 4.8.1.78
import state
import copy
import cv2
import os
import numpy as np
from ..moni.moni import *
from ..logger_module import logger
from lanrenauto.tools.screenshot import screenshot
def get_view(small_img, radius=70):
    """
    返回当前视野角度 感谢网友飞宇提供的识别方法
    Returns:以正北方向为0度计算

    """


    channels = cv2.split(small_img)
    # 提取Alpha通道
    alpha3 = channels[3]  # 索引为3，因为Alpha通道是第四个通道
    # cv2.imshow('Alpha Channel', alpha3)
    # cv2.waitKey(0)
    # cv2.destroyAllWindows()
    # _, alpha = cv2.threshold(small_img, 10, 255, cv2.THRESH_BINARY) #这个是提取蓝色箭头图像
    # 假设 gray_channel 是你的灰度图像
    # 创建一个掩膜，将150-200之间的值设为255，其他设为0
    mask = np.zeros_like(alpha3)
    mask[alpha3 >= 151] = 255
    mask[alpha3 < 151] = 0
    mask[alpha3 > 229] = 0
    img = np.zeros((radius * 2, radius * 2), dtype=np.uint8)
    center = (radius, radius)
    max_overlap = 0  # 最佳的重叠面积
    max_angle = -1  # 最佳的角度
    increment = 20  # 递增的角度，这里设置为10度
    for angle in range(0, 360, increment):
        img_copy = img.copy()
        # 画一个扇形
        cv2.ellipse(img_copy, center, (radius, radius), 225 + angle, 0, 90, 255, -1)
        overlap = np.logical_and(img_copy, mask).sum()
        if overlap > max_overlap:
            max_overlap = overlap
            max_angle = angle

    start_angle = max_angle - 10
    if start_angle < 0:
        start_angle = 0
        end_angle = start_angle + 10
    else:
        end_angle = start_angle + 20

    for angle in range(start_angle, end_angle, 1):
        img_copy = img.copy()
        # 画一个扇形
        cv2.ellipse(img_copy, center, (radius, radius), 225 + angle, 0, 90, 255, -1)
        overlap = np.logical_and(img_copy, mask).sum()
        if overlap > max_overlap:
            max_overlap = overlap
            max_angle = angle
    # print(f'最匹配角度:{max_angle}，重叠面积:{max_overlap}')
    # print(f'用时：{time.time() - old_time}')
    # cv2.destroyAllWindows()

    return max_angle
def get_view2(small_img, radius=70):
    """
    返回当前视野角度 感谢网友飞宇提供的识别方法
    Returns:以正北方向为0度计算

    """

    # radius = 70  # 定义截屏的宽度和长度
    # # 获取窗口句柄
    # hwnd = win32gui.FindWindow(state.GAME_CLASS, "原神")  # 替换成你实际的窗口句柄
    #
    #
    # small_img = screenshot(hwnd,(98+radius,55+radius), radius=radius)
    #
    #
    channels = cv2.split(small_img)
    # 提取Alpha通道
    alpha3 = channels[3]  # 索引为3，因为Alpha通道是第四个通道
    # cv2.imshow('Alpha Channel', alpha3)
    # cv2.waitKey(0)
    # cv2.destroyAllWindows()
    # _, alpha = cv2.threshold(small_img, 10, 255, cv2.THRESH_BINARY) #这个是提取蓝色箭头图像
    # 假设 gray_channel 是你的灰度图像
    # 创建一个掩膜，将150-200之间的值设为255，其他设为0
    mask = np.zeros_like(alpha3)
    mask[alpha3 >= 9] = 255
    mask[alpha3 < 9] = 0
    mask[alpha3 > 190] = 0
    img = np.zeros((radius * 2, radius * 2), dtype=np.uint8)
    center = (radius, radius)
    max_overlap = 0  # 最佳的重叠面积
    max_angle = -1  # 最佳的角度
    increment = 20  # 递增的角度，这里设置为10度
    for angle in range(0, 360, increment):
        img_copy = img.copy()
        # 画一个扇形
        cv2.ellipse(img_copy, center, (radius, radius), 225 + angle, 0, 90, 255, -1)
        overlap = np.logical_and(img_copy, mask).sum()
        if overlap > max_overlap:
            max_overlap = overlap
            max_angle = angle

    start_angle = max_angle - 10
    if start_angle < 0:
        start_angle = 0
        end_angle = start_angle + 10
    else:
        end_angle = start_angle + 20

    for angle in range(start_angle, end_angle, 1):
        img_copy = img.copy()
        # 画一个扇形
        cv2.ellipse(img_copy, center, (radius, radius), 225 + angle, 0, 90, 255, -1)
        overlap = np.logical_and(img_copy, mask).sum()
        if overlap > max_overlap:
            max_overlap = overlap
            max_angle = angle

    return max_angle
def pyramid_template_matching(image, template, mask=None):
    '''
    返回当前导航的角度
    :param image:
    :param template:
    :param mask:
    :return:
    '''
    result = image.copy()
    # 创建掩码
    if mask is not None:
        mask = mask.astype(np.uint8)
    # 金字塔层级
    # pyramid_levels =1
    best_angle = -1  # 最佳匹配角度
    max_similarity = 0  # 最大相似度
    best_res = 0
    best_rotated_template = ""
    h, w = template.shape[:2]
    scaled_mask = mask.astype(np.uint8)
    scaled_image = image
    scaled_template = template
    # for level in range(pyramid_levels):
    #     # 缩放大图和小图
    #     scaled_image = cv2.resize(image, (0, 0), fx=1 / (2 ** level), fy=1 / (2 ** level))
    #     scaled_template = cv2.resize(template, (0, 0), fx=1 / (2 ** level), fy=1 / (2 ** level))
    #     if mask is not None:
    #         scaled_mask = cv2.resize(mask, (0, 0), fx=1 / (2 ** level), fy=1 / (2 ** level))
    #         scaled_mask = scaled_mask.astype(np.uint8)
    # 旋转模板并进行匹配
    for angle in range(0, 360, 10):
        rotated_mask = rotate_image(scaled_mask, angle)
        rotated_mask[rotated_mask > 1] = 255  # 将灰色也变成白色
        rotated_template = rotate_image(scaled_template, angle)
        # save_rotated_image(rotated_template, "./datas/img", f"{angle}_template")
        # save_rotated_image(rotated_mask, "./datas/img",f"{angle}_mask")
        res = cv2.matchTemplate(scaled_image, rotated_template, cv2.TM_CCORR_NORMED, mask=rotated_mask)
        similarity = cv2.minMaxLoc(res)[1]  # 获取匹配相似度
        if similarity > max_similarity:
            max_similarity = similarity
            best_angle = angle
            best_res = res
            best_rotated_template = rotated_template

    best_angle_bk = copy.copy(best_angle)
    start_angle = best_angle_bk - 10
    if start_angle < 0:
        start_angle = 0
        end_angle = start_angle + 10
    else:
        end_angle = start_angle + 20
    for angle in range(start_angle, end_angle, 1):
        rotated_mask = rotate_image(scaled_mask, angle)
        rotated_mask[rotated_mask > 1] = 255  # 将灰色像素值改为黑色像素值
        rotated_template = rotate_image(scaled_template, angle)
        res = cv2.matchTemplate(scaled_image, rotated_template, cv2.TM_CCORR_NORMED, mask=rotated_mask)
        similarity = cv2.minMaxLoc(res)[1]  # 获取匹配相似度
        if similarity > max_similarity:
            max_similarity = similarity
            best_angle = angle
            best_res = res
            best_rotated_template = rotated_template

    # min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(best_res)
    # top_left = max_loc
    # bottom_right = (top_left[0] + w, top_left[1] + h)
    # 绘制角度
    # cv2.putText(result, f"{int(best_angle)}", (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
    # 在原始大图上绘制矩形框标记匹配区域
    # cv2.rectangle(result, top_left, bottom_right, (0, 255, 0), 2)
    return result, max_similarity, best_angle, best_rotated_template
def save_rotated_image(rotated_image, save_dir, index):
    save_path = os.path.join(save_dir, f"{index}.png")
    cv2.imwrite(save_path, rotated_image)
def rotate_image(img, angle):
    '''
    将图片旋转多少度
    :param img:
    :param angle:
    :return:
    '''
    h, w = img.shape[:2]
    center = (w // 2, h // 2)
    M = cv2.getRotationMatrix2D(center, -angle, 1.0)
    rotated_image = cv2.warpAffine(img, M, (w, h), flags=cv2.INTER_NEAREST, borderMode=cv2.BORDER_CONSTANT,
                                   borderValue=(0, 0, 0, 0))
    return rotated_image
def template_matching(img, template_img, threshold=0.8, maxcnt=0, mask=None):
    """
     模板匹配 多个
    Args:
        img:
        template_img:
        threshold:
        maxcnt:
        mask:

    Returns:

    """
    method = cv2.TM_CCOEFF_NORMED
    # 创建掩码
    if mask is not None:
        mask = mask.astype(np.uint8)
        scaled_mask = mask.astype(np.uint8)
    else:
        scaled_mask = mask


    scaled_image = img
    scaled_template = template_img

    if scaled_image is None:
        raise "大图不能为None"

    if scaled_template is None:
        raise "模板小图不能为None"


    h, w = scaled_template.shape[:2]
    res = cv2.matchTemplate(scaled_image, scaled_template, method, mask=scaled_mask)

    result = []
    while True:
        min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(res)
        if method in [cv2.TM_SQDIFF, cv2.TM_SQDIFF_NORMED]:
            top_left = min_loc
        else:
            top_left = max_loc
        if max_val < threshold or max_val == np.inf:
            break
        middle_point = [top_left[0] + w // 2, top_left[1] + h // 2]
        result.append(dict(
            result=middle_point,
            rectangle=(top_left, (top_left[0], top_left[1] + h), (top_left[0] + w, top_left[1]),
                       (top_left[0] + w, top_left[1] + h)),
            confidence=round(max_val, 2)
        ))

        if maxcnt and len(result) >= maxcnt:
            break
        cv2.floodFill(res, None, max_loc, (-1000,), max_val - threshold + 0.1, 1, flags=cv2.FLOODFILL_FIXED_RANGE)
    return result
def find_img_and_click(img, hwnd=0, left=0, top=0, right=1920, bottom=1080, p_left=0, p_top=0, label="", times=10,
                       step=1.0,
                       is_click=True, threshold=0.8, keepKey=None, mask=None, double_click=False, big_img=None,is_show=False):
    '''

    Args:
        img: 小图 np图
        hwnd: 句柄
        left: 左边
        top: 顶边
        right: 右边
        bottom: 底边
        p_left: 窗口偏移left  有些窗口有黑边/透明边
        p_top: 窗口偏移top
        label: 输出的日志
        times: 找图次数
        step: 查找间隔
        is_click: 是否点击
        threshold: 最小相似度
        keepKey: 需要一直按住哪个键? 不填 就不按 None
        mask: 掩码
        double_click: 是否双击
        big_img: None 自动截图  非None 则提供大图np图 提供大图为了保证点击成功 left 和 top也要准确给
        is_show: False 是否展示

    Returns:

    '''
    for _ in range(times):

        if not state.状态_循环开关:
            return False
        if keepKey is not None:
            pyautogui.keyDown(keepKey)
        if big_img is  None:
            big_img = screenshot(hwnd, left, top, right, bottom, None)
        res = template_matching(big_img, img, mask=mask, threshold=threshold)
        for r_item in res:
            result_post = [r_item["result"][0], r_item["result"][1]]
            logger.info(label)
            if is_show:
                # 转换为 NumPy 数组
                pts = np.array(
                    (
                        r_item['rectangle'][0], r_item['rectangle'][1], r_item['rectangle'][3],
                        r_item['rectangle'][2]),
                    np.int32)
                cv2.circle(big_img, result_post, 10, (0, 0, 255), -1)
                cv2.polylines(big_img, [pts], True, (0, 255, 0), 5)
                if big_img.shape[2] == 4:

                    state.图片_找图 = cv2.cvtColor(big_img, cv2.COLOR_BGRA2BGR)
                else:
                    state.图片_找图 = big_img
                state.QT_信号.mysig_show_xunlu.emit()
            if is_click:
                mouse_move(p_left + result_post[0] + left, p_top + result_post[1] + top)
                time.sleep(0.2)
                mouse_left_down()
                time.sleep(0.2)
                mouse_left_up()
                #print(p_left + result_post[0] + left, p_top + result_post[1] + top)
                if double_click:
                    mouse_left_down()
                    time.sleep(0.2)
                    mouse_left_up()
            return True
        if times > 1:
            time.sleep(step)
            big_img=None
    return False
def find_imgs_and_click(imgs, hwnd=0, left=0, top=0, right=1920, bottom=1080, p_left=0, p_top=0, label="", times=10,
                        step=1.0,
                        is_click=True, threshold=0.8, isMask=False,is_show=False):
    for _ in range(times):
        big_img = screenshot(hwnd, left, top, right, bottom, None)
        for img in imgs:
            if isMask:
                mask = img[:, :, 3]  # 提取透明度通道作为掩码
            else:
                mask = None
            ret = find_img_and_click(img, left, top, right, bottom, p_left, p_top, label=label, is_click=is_click,
                                     times=1, threshold=threshold, mask=mask, big_img=big_img,is_show=is_show)
            if ret:
                return True
        if times > 1:
            time.sleep(step)
    return False

def find_img_all_sift(big_img, small_img, roi=None, min_match_count=10, maxcnt=0):
    '''
    使用sift算法进行多个相同元素的查找
    Args:
        big_img(numpy.ndarray): 大图像
        small_img(numpy.ndarray): 需要查找的小图像
        roi(tuple): 感兴趣区域的坐标，格式为(x, y, width, height)
        min_match_count(int): 最小匹配数量
        maxcnt(int): 匹配的最大数量

    Returns:
        A list of found [{"result": point, "rectangle": rectangle, "confidence": 0.76}, ...]
        rectangle is a 4 points list
    '''
    try:
        sift = cv2.SIFT_create(sigma=1.3)
        flann = cv2.FlannBasedMatcher({'algorithm': 1, 'trees': 5}, dict(checks=50))
        g1 = cv2.cvtColor(small_img, cv2.COLOR_BGR2GRAY)


        if roi:
                x, y, width, height = roi
                roi_img = big_img[y:y + height, x:x + width]
                g2 = cv2.cvtColor(roi_img, cv2.COLOR_BGR2GRAY)
        else:
            g2 = cv2.cvtColor(big_img, cv2.COLOR_BGR2GRAY)

        #增强对比度
        # g1 = cv2.equalizeHist(g1)
        # g2 = cv2.equalizeHist(g2)
        # #自适应阈值分割 减少透明背景影响
        g1 = cv2.adaptiveThreshold(g1, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C,
                                             cv2.THRESH_BINARY, 21, 2)
        g2 = cv2.adaptiveThreshold(g2, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C,
                                           cv2.THRESH_BINARY, 21, 2)



        kp_sch, des_sch = sift.detectAndCompute(g1, None)
        if len(kp_sch) < min_match_count:
            return None
        kp_src, des_src = sift.detectAndCompute(g2, None)
        if len(kp_src) < min_match_count:
            return None

        result = []

        while True:
            matches = flann.knnMatch(des_sch, des_src, k=2)
            good = []

            for m, n in matches:
                if m.distance < 0.75 * n.distance:
                    good.append(m)
            if len(good) < min_match_count:
                break

            sch_pts = np.float32([kp_sch[m.queryIdx].pt for m in good]).reshape(-1, 1, 2)
            img_pts = np.float32([kp_src[m.trainIdx].pt for m in good]).reshape(-1, 1, 2)

            M, mask = cv2.findHomography(sch_pts, img_pts, cv2.RANSAC, 5.0)
            matches_mask = mask.ravel().tolist()

            h, w = small_img.shape[:2]
            pts = np.float32([[0, 0], [0, h-1], [w-1, h-1], [w-1, 0]]).reshape(-1, 1, 2)

            try:
                if roi:
                    dst = cv2.perspectiveTransform(pts, M) + [x, y]
                else:
                    dst = cv2.perspectiveTransform(pts, M)
            except:
                break

            pypts = []
            for npt in dst.astype(int).tolist():
                pypts.append(tuple(npt[0]))
            #保留小数点后两位

            sim =  round(min(1.0 * matches_mask.count(1) / len(matches_mask), 1.0),2)

            X = (pypts[0][0] + pypts[1][0] + pypts[2][0] + pypts[3][0]) // 4
            Y = (pypts[0][1] + pypts[1][1] + pypts[2][1] + pypts[3][1]) // 4
            middle_point = (X, Y)
            if 0 > middle_point[0] >  w or 0 > middle_point[1] > h:
                continue
            result.append(dict(
                result=middle_point,
                rectangle=pypts,
                confidence=sim,
                dst_pot=[np.int32(dst)]
            ))

            if maxcnt and len(result) >= maxcnt:
                break

            qindexes, tindexes = [], []
            for m in good:
                qindexes.append(m.queryIdx)
                tindexes.append(m.trainIdx)

            def filter_index(indexes, arr):
                r = []
                for i, item in enumerate(arr):
                    if i not in indexes:
                        r.append(item)
                return np.array(r)

            kp_src = filter_index(qindexes, kp_src)
            des_src = filter_index(tindexes, des_src)

        return result
    except:
        return None
def find_img_sift_and_click(img, hwnd=0, left=0, top=0, right=1920, bottom=1080, p_left=0, p_top=0, label="", times=10,
                       step=1.0,
                       is_click=True, threshold=0.8, keepKey=None, roi=None, double_click=False, big_img=None,is_show=False):
    '''

    Args:
        img: 小图 np图
        hwnd: 句柄
        left: 左边
        top: 顶边
        right: 右边
        bottom: 底边
        p_left: 窗口偏移left  有些窗口有黑边/透明边
        p_top: 窗口偏移top
        label: 输出的日志
        times: 找图次数
        step: 查找间隔
        is_click: 是否点击
        threshold: 最小相似度
        keepKey: 需要一直按住哪个键? 不填 就不按 None
        roi: 感兴趣的区域 默认为none 全图搜索  (tuple): 感兴趣区域的坐标，格式为(x, y, width, height)
        double_click: 是否双击
        big_img: None 自动截图  非None 则提供大图np图 提供大图为了保证点击成功 left 和 top也要准确给
        is_show: False 是否展示

    Returns:

    '''
    for _ in range(times):

        if not state.状态_循环开关:
            return False
        if keepKey is not None:
            pyautogui.keyDown(keepKey)
        if big_img is  None:
            big_img = screenshot(hwnd, left, top, right, bottom, None)
        res = find_img_all_sift(big_img, img,roi)

        for r_item in res:
            if r_item['confidence'] <threshold:
                continue
            result_post = [ r_item["middle_point"][0], r_item["middle_point"][1]]
            logger.info(label)
            if is_show:
                cv2.circle(big_img, result_post, 10, (0, 0, 255), -1)
                cv2.polylines(big_img, result['dst_pot'], True, (0, 255, 0), 5)
                # cv2.imshow('Genshin navigation'  , big_img)
                # cv2.imwrite("./output.jpg", big_img)
                if big_img.shape[2] == 4:
                    state.图片_找图 = cv2.cvtColor(big_img, cv2.COLOR_BGRA2BGR)
                else:
                    state.图片_找图 = big_img
                state.QT_信号.mysig_show_xunlu.emit()
            if is_click:
                mouse_move(p_left + result_post[0] + left, p_top + result_post[1] + top)
                time.sleep(0.2)
                mouse_left_down()
                time.sleep(0.2)
                mouse_left_up()
                #print(p_left + result_post[0] + left, p_top + result_post[1] + top)
                if double_click:
                    mouse_left_down()
                    time.sleep(0.2)
                    mouse_left_up()
            return True
        if times > 1:
            time.sleep(step)
            big_img=None
    return False
def find_imgs_sift_and_click(imgs, hwnd=0, left=0, top=0, right=1920, bottom=1080, p_left=0, p_top=0, label="", times=10,
                        step=1.0,
                        is_click=True, threshold=0.8, roi=None,is_show=False):
    '''
    使用sift算法 进行搜图 支持形变 放大缩小 透明的干扰的图
    Args:
        img: 小图 np图 列表
        hwnd: 句柄
        left: 左边
        top: 顶边
        right: 右边
        bottom: 底边
        p_left: 窗口偏移left  有些窗口有黑边/透明边
        p_top: 窗口偏移top
        label: 输出的日志
        times: 找图次数
        step: 查找间隔
        is_click: 是否点击
        threshold: 最小相似度
        roi: 感兴趣的区域 默认为none 全图搜索  (tuple): 感兴趣区域的坐标，格式为(x, y, width, height)
        is_show: False 是否展示

    Returns:
    '''
    for _ in range(times):
        big_img = screenshot(hwnd, left, top, right, bottom, None)
        for img in imgs:
            ret = find_img_sift_and_click(img, left, top, right, bottom, p_left, p_top, label=label, is_click=is_click,
                                     times=1, threshold=threshold, roi=roi, big_img=big_img,is_show=is_show)
            if ret:
                return True
        if times > 1:
            time.sleep(step)
    return False
def find_dict_index(dict_list, key, value):
    '''
    查找列表成员中的 字典里指定key 中是否存在某个值 返回相似度最高的成员索引

    :param dict_list:
    :param key:
    :param value:
    :return:
    '''
    max_sim = 0
    index = None
    for i, dictionary in enumerate(dict_list):
        if dictionary.get(key) == value:
            if dictionary.get('sim') > max_sim:
                max_sim = dictionary.get('sim')
                index = i
    return index != None, index
def find_dict_num(dict_list, key, value):
    '''
    列表中 字典里指定key 存在某个值的次数
    :param dict_list:
    :param key:
    :param value:
    :return:
    '''
    num_ = 0
    for i, dictionary in enumerate(dict_list):
        if dictionary.get(key) == value:
            num_ += 1
    return num_

if __name__ == '__main__':
    # 读取图像和模板
    # old_time=time.time()
    # image = cv2.imdecode(np.fromfile(file=r"大图.png", dtype=np.uint8), cv2.IMREAD_UNCHANGED)  # 加载大图
    # template = cv2.imdecode(np.fromfile(file=r"小图.png", dtype=np.uint8), cv2.IMREAD_UNCHANGED)  # 加载透明图
    # mask = template[:, :, 3]  # 提取透明度通道作为掩码
    # result,max_similarity, best_angle = pyramid_template_matching(image, template, mask=mask)
    # print(f"相似度:{max_similarity} 角度:{best_angle}  用时:{int((time.time()-old_time)*1000)}ms")
    #
    # # 显示结果图像
    # cv2.imshow("Result", result)
    # cv2.waitKey(0)
    # cv2.destroyAllWindows()

    old_time = time.time()
    image = cv2.imdecode(np.fromfile(file=r"D:\QQ聊天记录\111.bmp", dtype=np.uint8), cv2.IMREAD_COLOR)  # 加载大图
    template = cv2.imdecode(np.fromfile(file=r"D:\QQ聊天记录\p.png", dtype=np.uint8), cv2.IMREAD_COLOR)  # 加载透明图

    RET = template_matching(image, template, mask=None)
    for result in RET:
        print(result)
        # 转换为 NumPy 数组
        pts = np.array((result['rectangle'][0], result['rectangle'][1], result['rectangle'][3],
                        result['rectangle'][2]), np.int32)

        cv2.polylines(image, [pts], True, (0, 255, 0), 5)

        # 显示结果图像
        cv2.imshow("324234", result)
        cv2.waitKey(0)
        cv2.destroyAllWindows()
