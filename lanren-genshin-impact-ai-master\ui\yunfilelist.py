# -*- coding: utf-8 -*-

# Form implementation generated from reading ui file 'yunfilelist.ui'
#
# Created by: PyQt5 UI code generator 5.15.7
#
# WARNING: Any manual changes made to this file will be lost when pyuic5 is
# run again.  Do not edit this file unless you know what you are doing.


from PyQt5 import Qt<PERSON><PERSON>, QtGui, QtWidgets


class Ui_form_yunfilelist(object):
    def setupUi(self, form_yunfilelist):
        form_yunfilelist.setObjectName("form_yunfilelist")
        form_yunfilelist.resize(591, 541)
        self.verticalLayout = QtWidgets.QVBoxLayout(form_yunfilelist)
        self.verticalLayout.setContentsMargins(1, 1, 1, 1)
        self.verticalLayout.setSpacing(1)
        self.verticalLayout.setObjectName("verticalLayout")
        self.tabWidget = QtWidgets.QTabWidget(form_yunfilelist)
        self.tabWidget.setMaximumSize(QtCore.QSize(16777215, 30))
        self.tabWidget.setObjectName("tabWidget")
        self.tab_7 = QtWidgets.QWidget()
        self.tab_7.setObjectName("tab_7")
        self.tabWidget.addTab(self.tab_7, "")
        self.tab = QtWidgets.QWidget()
        self.tab.setObjectName("tab")
        self.tabWidget.addTab(self.tab, "")
        self.tab_2 = QtWidgets.QWidget()
        self.tab_2.setObjectName("tab_2")
        self.tabWidget.addTab(self.tab_2, "")
        self.tab_3 = QtWidgets.QWidget()
        self.tab_3.setObjectName("tab_3")
        self.tabWidget.addTab(self.tab_3, "")
        self.tab_4 = QtWidgets.QWidget()
        self.tab_4.setObjectName("tab_4")
        self.tabWidget.addTab(self.tab_4, "")
        self.tab_5 = QtWidgets.QWidget()
        self.tab_5.setObjectName("tab_5")
        self.tabWidget.addTab(self.tab_5, "")
        self.tab_6 = QtWidgets.QWidget()
        self.tab_6.setObjectName("tab_6")
        self.tabWidget.addTab(self.tab_6, "")
        self.verticalLayout.addWidget(self.tabWidget)
        self.sa_main = QtWidgets.QScrollArea(form_yunfilelist)
        self.sa_main.setWidgetResizable(True)
        self.sa_main.setObjectName("sa_main")
        self.scrollAreaWidgetContents = QtWidgets.QWidget()
        self.scrollAreaWidgetContents.setGeometry(QtCore.QRect(0, 0, 587, 506))
        self.scrollAreaWidgetContents.setObjectName("scrollAreaWidgetContents")
        self.sa_main.setWidget(self.scrollAreaWidgetContents)
        self.verticalLayout.addWidget(self.sa_main)

        self.retranslateUi(form_yunfilelist)
        self.tabWidget.setCurrentIndex(0)
        QtCore.QMetaObject.connectSlotsByName(form_yunfilelist)

    def retranslateUi(self, form_yunfilelist):
        _translate = QtCore.QCoreApplication.translate
        form_yunfilelist.setWindowTitle(_translate("form_yunfilelist", "Form"))
        self.tabWidget.setTabText(self.tabWidget.indexOf(self.tab_7), _translate("form_yunfilelist", "全部"))
        self.tabWidget.setTabText(self.tabWidget.indexOf(self.tab), _translate("form_yunfilelist", "副本"))
        self.tabWidget.setTabText(self.tabWidget.indexOf(self.tab_2), _translate("form_yunfilelist", "回放"))
        self.tabWidget.setTabText(self.tabWidget.indexOf(self.tab_3), _translate("form_yunfilelist", "锄地"))
        self.tabWidget.setTabText(self.tabWidget.indexOf(self.tab_4), _translate("form_yunfilelist", "材料"))
        self.tabWidget.setTabText(self.tabWidget.indexOf(self.tab_5), _translate("form_yunfilelist", "连招"))
        self.tabWidget.setTabText(self.tabWidget.indexOf(self.tab_6), _translate("form_yunfilelist", "其它"))
