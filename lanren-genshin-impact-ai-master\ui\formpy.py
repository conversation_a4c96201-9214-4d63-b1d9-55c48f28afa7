# -*- coding: utf-8 -*-

# Form implementation generated from reading ui file 'formpy.ui'
#
# Created by: PyQt5 UI code generator 5.15.7
#
# WARNING: Any manual changes made to this file will be lost when pyuic5 is
# run again.  Do not edit this file unless you know what you are doing.


from PyQt5 import Qt<PERSON>ore, QtGui, QtWidgets


class Ui_formpy(object):
    def setupUi(self, formpy):
        formpy.setObjectName("formpy")
        formpy.resize(316, 196)
        self.gridLayout = QtWidgets.QGridLayout(formpy)
        self.gridLayout.setObjectName("gridLayout")
        self.horizontalLayout = QtWidgets.QHBoxLayout()
        self.horizontalLayout.setObjectName("horizontalLayout")
        self.led_name = QtWidgets.QLineEdit(formpy)
        self.led_name.setMinimumSize(QtCore.QSize(0, 0))
        self.led_name.setMaximumSize(QtCore.QSize(999, 9999))
        self.led_name.setStyleSheet("color: rgb(255, 255, 255);")
        self.led_name.setAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignLeft|QtCore.Qt.AlignVCenter)
        self.led_name.setObjectName("led_name")
        self.horizontalLayout.addWidget(self.led_name)
        self.cb_is_checked = QtWidgets.QCheckBox(formpy)
        self.cb_is_checked.setMinimumSize(QtCore.QSize(0, 0))
        self.cb_is_checked.setMaximumSize(QtCore.QSize(999999, 9999999))
        self.cb_is_checked.setLayoutDirection(QtCore.Qt.RightToLeft)
        self.cb_is_checked.setStyleSheet("")
        self.cb_is_checked.setText("")
        self.cb_is_checked.setChecked(True)
        self.cb_is_checked.setObjectName("cb_is_checked")
        self.horizontalLayout.addWidget(self.cb_is_checked)
        self.gridLayout.addLayout(self.horizontalLayout, 0, 0, 1, 4)
        self.bt_arg = QtWidgets.QPushButton(formpy)
        self.bt_arg.setMinimumSize(QtCore.QSize(50, 50))
        self.bt_arg.setObjectName("bt_arg")
        self.gridLayout.addWidget(self.bt_arg, 1, 1, 1, 1)
        self.bt_opendir = QtWidgets.QPushButton(formpy)
        self.bt_opendir.setMinimumSize(QtCore.QSize(50, 50))
        self.bt_opendir.setMaximumSize(QtCore.QSize(99999, 99999))
        self.bt_opendir.setObjectName("bt_opendir")
        self.gridLayout.addWidget(self.bt_opendir, 1, 2, 1, 1)
        self.bt_start = QtWidgets.QPushButton(formpy)
        self.bt_start.setMinimumSize(QtCore.QSize(50, 50))
        self.bt_start.setMaximumSize(QtCore.QSize(999999, 999999))
        self.bt_start.setObjectName("bt_start")
        self.gridLayout.addWidget(self.bt_start, 1, 3, 1, 1)
        self.bt_chuansong = QtWidgets.QPushButton(formpy)
        self.bt_chuansong.setMinimumSize(QtCore.QSize(50, 50))
        self.bt_chuansong.setMaximumSize(QtCore.QSize(999999, 999999))
        self.bt_chuansong.setObjectName("bt_chuansong")
        self.gridLayout.addWidget(self.bt_chuansong, 2, 0, 1, 1)
        self.bt_moban = QtWidgets.QPushButton(formpy)
        self.bt_moban.setMinimumSize(QtCore.QSize(50, 50))
        self.bt_moban.setMaximumSize(QtCore.QSize(99999, 99999))
        self.bt_moban.setObjectName("bt_moban")
        self.gridLayout.addWidget(self.bt_moban, 2, 1, 1, 1)
        self.bt_moban_maodian = QtWidgets.QPushButton(formpy)
        self.bt_moban_maodian.setMinimumSize(QtCore.QSize(50, 50))
        self.bt_moban_maodian.setMaximumSize(QtCore.QSize(999, 9999))
        self.bt_moban_maodian.setObjectName("bt_moban_maodian")
        self.gridLayout.addWidget(self.bt_moban_maodian, 2, 2, 1, 1)
        self.bt_del = QtWidgets.QPushButton(formpy)
        self.bt_del.setMinimumSize(QtCore.QSize(50, 50))
        self.bt_del.setMaximumSize(QtCore.QSize(99999, 99999))
        self.bt_del.setObjectName("bt_del")
        self.gridLayout.addWidget(self.bt_del, 2, 3, 1, 1)

        self.retranslateUi(formpy)
        QtCore.QMetaObject.connectSlotsByName(formpy)

    def retranslateUi(self, formpy):
        _translate = QtCore.QCoreApplication.translate
        formpy.setWindowTitle(_translate("formpy", "Form"))
        self.led_name.setText(_translate("formpy", "蒙德锄地"))
        self.bt_arg.setText(_translate("formpy", "参数"))
        self.bt_opendir.setText(_translate("formpy", "打开目录"))
        self.bt_start.setText(_translate("formpy", "启动"))
        self.bt_chuansong.setText(_translate("formpy", "传送\n"
"脚本"))
        self.bt_moban.setText(_translate("formpy", "传送\n"
"模板"))
        self.bt_moban_maodian.setText(_translate("formpy", "锚点\n"
"模板"))
        self.bt_del.setText(_translate("formpy", "删除\n"
"任务"))
