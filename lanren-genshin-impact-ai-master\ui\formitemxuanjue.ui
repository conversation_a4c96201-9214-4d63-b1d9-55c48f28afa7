<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>formitemxuanjue</class>
 <widget class="QWidget" name="formitemxuanjue">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>286</width>
    <height>172</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <layout class="QGridLayout" name="gridLayout">
   <item row="0" column="0" colspan="4">
    <layout class="QHBoxLayout" name="horizontalLayout">
     <item>
      <widget class="QLineEdit" name="led_name">
       <property name="minimumSize">
        <size>
         <width>0</width>
         <height>0</height>
        </size>
       </property>
       <property name="maximumSize">
        <size>
         <width>999</width>
         <height>9999</height>
        </size>
       </property>
       <property name="styleSheet">
        <string notr="true">color: rgb(255, 255, 255);</string>
       </property>
       <property name="text">
        <string>蒙德锄地</string>
       </property>
       <property name="alignment">
        <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QCheckBox" name="cb_is_checked">
       <property name="minimumSize">
        <size>
         <width>0</width>
         <height>0</height>
        </size>
       </property>
       <property name="maximumSize">
        <size>
         <width>999999</width>
         <height>9999999</height>
        </size>
       </property>
       <property name="layoutDirection">
        <enum>Qt::RightToLeft</enum>
       </property>
       <property name="styleSheet">
        <string notr="true"/>
       </property>
       <property name="text">
        <string/>
       </property>
       <property name="checked">
        <bool>true</bool>
       </property>
      </widget>
     </item>
    </layout>
   </item>
   <item row="1" column="0">
    <widget class="QLabel" name="lb_zhanchang">
     <property name="minimumSize">
      <size>
       <width>50</width>
       <height>50</height>
      </size>
     </property>
     <property name="text">
      <string>站场:</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignCenter</set>
     </property>
    </widget>
   </item>
   <item row="1" column="1">
    <widget class="QLineEdit" name="led_main">
     <property name="minimumSize">
      <size>
       <width>50</width>
       <height>50</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>50</width>
       <height>50</height>
      </size>
     </property>
     <property name="font">
      <font>
       <family>ADMUI3Lg</family>
       <pointsize>14</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">color: rgb(255, 255, 255);</string>
     </property>
     <property name="text">
      <string>4</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignCenter</set>
     </property>
    </widget>
   </item>
   <item row="1" column="2">
    <widget class="QComboBox" name="cbb_1">
     <property name="minimumSize">
      <size>
       <width>50</width>
       <height>50</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>9999</width>
       <height>9999</height>
      </size>
     </property>
     <property name="styleSheet">
      <string notr="true">color: rgb(255, 255, 255);
margin: 0px; 
padding: 0px;
</string>
     </property>
     <property name="editable">
      <bool>false</bool>
     </property>
     <property name="insertPolicy">
      <enum>QComboBox::NoInsert</enum>
     </property>
     <property name="duplicatesEnabled">
      <bool>false</bool>
     </property>
     <property name="frame">
      <bool>true</bool>
     </property>
    </widget>
   </item>
   <item row="1" column="3">
    <widget class="QPushButton" name="bt_start">
     <property name="minimumSize">
      <size>
       <width>50</width>
       <height>50</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>999999</width>
       <height>999999</height>
      </size>
     </property>
     <property name="text">
      <string>启动</string>
     </property>
    </widget>
   </item>
   <item row="2" column="0">
    <widget class="QComboBox" name="cbb_2">
     <property name="minimumSize">
      <size>
       <width>50</width>
       <height>50</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>9999</width>
       <height>9999</height>
      </size>
     </property>
     <property name="styleSheet">
      <string notr="true">color: rgb(255, 255, 255);
margin: 0px; 
padding: 0px;</string>
     </property>
     <property name="editable">
      <bool>false</bool>
     </property>
     <property name="insertPolicy">
      <enum>QComboBox::NoInsert</enum>
     </property>
     <property name="duplicatesEnabled">
      <bool>false</bool>
     </property>
     <property name="frame">
      <bool>true</bool>
     </property>
    </widget>
   </item>
   <item row="2" column="1">
    <widget class="QComboBox" name="cbb_3">
     <property name="minimumSize">
      <size>
       <width>50</width>
       <height>50</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>9999</width>
       <height>9999</height>
      </size>
     </property>
     <property name="styleSheet">
      <string notr="true">color: rgb(255, 255, 255);
margin: 0px; 
padding: 0px;</string>
     </property>
     <property name="editable">
      <bool>false</bool>
     </property>
     <property name="insertPolicy">
      <enum>QComboBox::NoInsert</enum>
     </property>
     <property name="duplicatesEnabled">
      <bool>false</bool>
     </property>
     <property name="frame">
      <bool>true</bool>
     </property>
    </widget>
   </item>
   <item row="2" column="2">
    <widget class="QComboBox" name="cbb_4">
     <property name="minimumSize">
      <size>
       <width>50</width>
       <height>50</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>9999</width>
       <height>9999</height>
      </size>
     </property>
     <property name="styleSheet">
      <string notr="true">color: rgb(255, 255, 255);
margin: 0px; 
padding: 0px;</string>
     </property>
     <property name="editable">
      <bool>false</bool>
     </property>
     <property name="insertPolicy">
      <enum>QComboBox::NoInsert</enum>
     </property>
     <property name="duplicatesEnabled">
      <bool>false</bool>
     </property>
     <property name="frame">
      <bool>true</bool>
     </property>
    </widget>
   </item>
   <item row="2" column="3">
    <widget class="QPushButton" name="bt_del">
     <property name="minimumSize">
      <size>
       <width>50</width>
       <height>50</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>999999</width>
       <height>999999</height>
      </size>
     </property>
     <property name="text">
      <string>删除
任务</string>
     </property>
    </widget>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
