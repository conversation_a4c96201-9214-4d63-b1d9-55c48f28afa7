import os
import sys
from PyQt5.Qt import *
from PyQt5.QtGui import *
from PyQt5.QtCore import *

class ShutdownConfirmationDialog(QDialog):
    def __init__(self, parent=None):
        super().__init__(parent,Qt.Dialog | Qt.WindowStaysOnTopHint)
        self.initUI()



    def initUI(self):
        self.resize(300, 100)
        self.setWindowTitle('关机确认')
        self.confirmButton = QPushButton('确定', self)
        self.confirmButton.clicked.connect(self.shutdown)
        self.confirmButton.setGeometry(50, 50, 75, 30)

        self.cancelButton = QPushButton('取消', self)
        self.cancelButton.clicked.connect(self.cancelShutdown)
        self.cancelButton.setGeometry(150, 50, 75, 30)

        self.timerLabel = QLabel('倒计时：20', self)
        self.timerLabel.setGeometry(220, 10, 100, 30)

        self.timer = QTimer(self)
        self.timer.timeout.connect(self.countdown)
        self.secondsLeft = 20
        self.timer.start(1000)

    def countdown(self):
        if self.secondsLeft < 0:
            return
        self.secondsLeft -= 1
        self.timerLabel.setText(f'倒计时：{self.secondsLeft}')
        if self.secondsLeft == 0:
            self.shutdown()

    def shutdown(self):
        os.system('shutdown -s -t 10')
        qApp.quit()
        # 调用关机命令

    def cancelShutdown(self):
        self.timer.stop()
        self.close()



# if __name__ == '__main__':
#     app = QApplication(sys.argv)
#     dialog = ShutdownConfirmationDialog()
#     dialog.show()
#     sys.exit(app.exec_())