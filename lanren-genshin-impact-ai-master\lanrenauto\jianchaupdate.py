import html

import requests

def get_var_data():
    try:
        headers = {
            'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/117.0.0.0 Safari/537.36 Edg/117.0.2045.47"
        }
        res=requests.get("https://note.youdao.com/yws/api/personal/preview/WEB96c82d6b37c9fd60f8b1d146731080d6?method=convert&shareToken=f3fb8b827c2fbcd651073cf7b55a6b3e&engine=nyozo",headers=headers)
        if res.status_code==200:
            return html.unescape(res.text).split("<pre>")[-1].split("</pre>")[0].split("\n")[:-1]
        else:
            return False
    except:
        print("请不要开梯子")
        return False
def get_updatetxt():
    try:
        headers = {
            'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/117.0.0.0 Safari/537.36 Edg/117.0.2045.47"
        }
        res = requests.get("https://note.youdao.com/yws/api/personal/preview/WEB9b9f3f25e3921ae407edf8ba9537b847?method=convert&shareToken=8b937504aee5dcb1f8c62678d7fffdad&engine=nyozo",headers=headers)
        if res.status_code == 200:
            return html.unescape(res.text).split("<pre>")[-1].split("</pre>")[0][:-1]
        else:
            return ""
    except:
        print("无法获取更新日志,请不要开梯子!")
        return ""
if __name__ == '__main__':

    print(get_var_data())
    print(get_updatetxt())
