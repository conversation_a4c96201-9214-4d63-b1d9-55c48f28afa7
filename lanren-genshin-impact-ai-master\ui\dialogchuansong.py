# -*- coding: utf-8 -*-

# Form implementation generated from reading ui file 'dialogchuansong.ui'
#
# Created by: PyQt5 UI code generator 5.15.7
#
# WARNING: Any manual changes made to this file will be lost when pyuic5 is
# run again.  Do not edit this file unless you know what you are doing.


from PyQt5 import QtCore, QtGui, QtWidgets


class Ui_dialogchuansong(object):
    def setupUi(self, dialogchuansong):
        dialogchuansong.setObjectName("dialogchuansong")
        dialogchuansong.resize(408, 156)
        self.gridLayout = QtWidgets.QGridLayout(dialogchuansong)
        self.gridLayout.setObjectName("gridLayout")
        self.label_5 = QtWidgets.QLabel(dialogchuansong)
        self.label_5.setObjectName("label_5")
        self.gridLayout.addWidget(self.label_5, 0, 0, 1, 1)
        self.cbb_address = QtWidgets.QComboBox(dialogchuansong)
        self.cbb_address.setMinimumSize(QtCore.QSize(30, 30))
        self.cbb_address.setStyleSheet("color: rgb(255, 255, 255);\n"
"margin: 0px; \n"
"padding: 0px;")
        self.cbb_address.setObjectName("cbb_address")
        self.cbb_address.addItem("")
        self.gridLayout.addWidget(self.cbb_address, 0, 1, 1, 2)
        self.bt_huifang = QtWidgets.QPushButton(dialogchuansong)
        self.bt_huifang.setMinimumSize(QtCore.QSize(50, 50))
        self.bt_huifang.setObjectName("bt_huifang")
        self.gridLayout.addWidget(self.bt_huifang, 2, 2, 1, 1)
        self.bt_luzhi = QtWidgets.QPushButton(dialogchuansong)
        self.bt_luzhi.setMinimumSize(QtCore.QSize(50, 50))
        self.bt_luzhi.setObjectName("bt_luzhi")
        self.gridLayout.addWidget(self.bt_luzhi, 2, 0, 1, 1)
        self.label = QtWidgets.QLabel(dialogchuansong)
        self.label.setObjectName("label")
        self.gridLayout.addWidget(self.label, 1, 0, 1, 2)
        self.bt_wenjianxuan = QtWidgets.QPushButton(dialogchuansong)
        self.bt_wenjianxuan.setMinimumSize(QtCore.QSize(50, 50))
        self.bt_wenjianxuan.setObjectName("bt_wenjianxuan")
        self.gridLayout.addWidget(self.bt_wenjianxuan, 2, 1, 1, 1)
        self.clb_jiaocheng = QtWidgets.QCommandLinkButton(dialogchuansong)
        self.clb_jiaocheng.setObjectName("clb_jiaocheng")
        self.gridLayout.addWidget(self.clb_jiaocheng, 1, 2, 1, 1)

        self.retranslateUi(dialogchuansong)
        QtCore.QMetaObject.connectSlotsByName(dialogchuansong)

    def retranslateUi(self, dialogchuansong):
        _translate = QtCore.QCoreApplication.translate
        dialogchuansong.setWindowTitle(_translate("dialogchuansong", "设置传送脚本"))
        self.label_5.setText(_translate("dialogchuansong", "选择区域:"))
        self.cbb_address.setItemText(0, _translate("dialogchuansong", "手动录制"))
        self.bt_huifang.setText(_translate("dialogchuansong", "回放"))
        self.bt_luzhi.setText(_translate("dialogchuansong", "录制"))
        self.label.setText(_translate("dialogchuansong", "请选择一个配置方式:"))
        self.bt_wenjianxuan.setText(_translate("dialogchuansong", "文件中选择"))
        self.clb_jiaocheng.setText(_translate("dialogchuansong", "教程"))
