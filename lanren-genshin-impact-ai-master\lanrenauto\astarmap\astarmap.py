# -*- coding: utf-8 -*-
import cv2
import numpy as np
from PIL import Image
class AStarMap:
    def __init__(self,):
        '''
        A星寻路类
        '''
        self.map_matrix=[]
        self.goal=(0,0)
    def load_map(self,image_path):
        '''
        加载地图 这个地图要障碍物地图
        :param image_path:
        :return:
        '''
        # 读取图片
        self.image = Image.open(image_path)
        # 获取图片尺寸
        self.width, self.height = self.image.size
        # 定义地图
        self.map_matrix = []
        # 根据像素颜色生成地图数据
        for x in range(self.width):
            row = []
            for y in range(self.height):
                pixel = self.image.getpixel((x, y))
                if pixel[0] >= 240 and pixel[1]  >=  240 and pixel[2]  >= 240:  # 如果是白色
                    row.append(0)
                elif pixel[0] >= 200 and pixel[1] < 100 and pixel[2] < 100:#紅色
                    self.goal=(x, y)
                    row.append(0)

                else:
                    row.append(1)




            self.map_matrix.append(row)
    def draw_path_on_image(self,image_path, path, output_path=None):
        '''
        将路线画在地图上
        :param image_path:
        :param path:
        :param output_path:
        :return:
        '''
        try:
            # 将路径转换为NumPy数组
            path = np.array(path)

            # 将路径坐标转换为OpenCV需要的格式
            path = path.reshape((-1, 1, 2)).astype(np.int32)
        except:
            pass

        # 读取图片并转为RGB格式
        image = cv2.imdecode(np.fromfile(file=image_path, dtype=np.uint8), cv2.IMREAD_COLOR)  # 加载大图
        #image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        try:
            # 绘制路径
            cv2.polylines(image, [path], False, (255, 0, 0), thickness=2)
        except:
            pass

        # 显示或保存结果
        if output_path is not None:
            cv2.imwrite(output_path, image)
        else:
            cv2.imshow("Path on Image", image)
            cv2.waitKey(0)
    def heuristic_func(self,current_state, goal):
        '''
        计算当前状态到目标状态的启发式函数
        :param current_state:
        :param goal:
        :return:
        '''
        return abs(current_state[0] - goal[0]) + abs(current_state[1] - goal[1])
    def successors_func(self,current_state, map_matrix):
        '''
        获取当前状态的8个后继状态
        :param current_state:
        :param map_matrix:
        :return:
        '''
        successors = []
        directions = [(0, 1), (0, -1), (1, 0), (-1, 0), (1, 1), (1, -1), (-1, 1), (-1, -1)]  # 八个方向

        for direction in directions:
            new_state = (current_state[0] + direction[0], current_state[1] + direction[1])
            if new_state[0] < 0 or new_state[0] >= len(map_matrix) or new_state[1] < 0 or new_state[1] >= len(map_matrix[0]) or map_matrix[new_state[0]][new_state[1]]:
                    continue

            cost = 1  # 对角线移动的代价设为1（或者根号2），根据具体情况调整
            successors.append((new_state, cost))

        return successors
    def astar(self,start_state,goal=None):
        '''
        A*算法
        :param start_state:
        :param goal:
        :return:
        '''
        if goal==None:
            goal=self.goal
        open_list = [(start_state, 0)]  # 存储待探索的节点和对应的路径代价
        closed_list = set()  # 存储已经探索过的节点
        came_from = {}
        g_score = {start_state: 0}  # 存储起点到每个节点的实际代价
        f_score = {start_state: self.heuristic_func(start_state, goal)}  # 存储起点经过每个节点到目标节点的总代价估计

        while open_list:
            current_state, current_cost = min(open_list, key=lambda x: x[1] + f_score[x[0]])  # 选择路径最短的节点
            open_list.remove((current_state, current_cost))

            if current_state == goal:
                path = [current_state]
                while current_state in came_from:
                    current_state = came_from[current_state]
                    path.append(current_state)
                return path[::-1]

            closed_list.add(current_state)

            for successor_state, step_cost in self.successors_func(current_state, self.map_matrix):
                if successor_state in closed_list:
                    continue

                tentative_g_score = g_score[current_state] + step_cost
                if successor_state not in g_score or tentative_g_score < g_score[successor_state]:
                    came_from[successor_state] = current_state
                    g_score[successor_state] = tentative_g_score
                    f_score[successor_state] = tentative_g_score + self.heuristic_func(successor_state, goal)
                    if (successor_state, f_score[successor_state]) not in open_list:
                        open_list.append((successor_state, f_score[successor_state]))

        return None

if __name__ == '__main__':
    image_path=r"datas/max_map/蒙德1.png"
    image_path_barrier = r"datas/max_map/蒙德1_障碍物.png"
    # 定义起点和终点
    start = (399,628)
    ast = AStarMap()
    ast.load_map(image_path_barrier)
    print(ast.goal)
    # 执行A*算法并打印结果
    path = ast.astar(start )
    if path:
        print("找到路径：")
        for position in path:
            print(position)
        ast.draw_path_on_image(image_path, path)

    else:
        print("无法找到路径！")
        ast.draw_path_on_image(image_path_barrier, path)
