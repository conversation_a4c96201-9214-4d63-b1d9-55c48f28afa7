import time

import requests
import json

class YunPan():
   def __init__(self):
      self.token=""
      self.url=""
   def login(self,username="",password=""):
      return {}
   def get_file_list(self,dir_home="原神AI"):

      return []
   def get_file_get(self,file_name=""):
      return {}
   def get_file_all(self,dir_="原神AI",file_data=[],isclose=False):
      '''
      递归遍历所有的文件
      :param dir_:目录
      :param file_data: 上一次的数据
      :param isclose:用来强制重置数据的 防止重复
      :return:
      '''
      return []


