# -*- coding: utf-8 -*-
import os
import random
import time
import traceback
import numpy as np
import win32gui
import win32ui
import win32con
from PIL import Image

from ..logger_module import logger


def screenshot(hwnd, left=0, top=0, right=0, bottom=0, filename=None):
    try:
        #old_time=time.time()
        width = right-left
        height = bottom - top
        # 判断窗口是否可见
        if not win32gui.IsWindowVisible(hwnd):
            return False
        # 创建设备描述表
        hwnd_dc = win32gui.GetDC(hwnd)   #GetWindowDC
        mfc_dc = win32ui.CreateDCFromHandle(hwnd_dc)
        save_dc = mfc_dc.CreateCompatibleDC()

        # 创建位图对象
        save_bitmap = win32ui.CreateBitmap()
        save_bitmap.CreateCompatibleBitmap(mfc_dc, width, height)

        # 将位图对象绑定到设备描述表
        save_dc.SelectObject(save_bitmap)
        # result = windll.user32.PrintWindow(hwnd, save_dc.GetSafeHdc(),1)   #0  1 或者3  3没有透明通道信息
        # if result == 0:
        #    print("PrintWindow failed")
        #    return False
        # # 将截图保存到位图对象中
        save_dc.BitBlt((0, 0), (width, height), mfc_dc, (left, top), win32con.SRCCOPY)#win32con.CAPTUREBLT  win32con.SRCCOPY

        # 将位图对象转换为OpenCV图像
        bmp_info = save_bitmap.GetInfo()
        bmp_str = save_bitmap.GetBitmapBits(True)
        img = np.frombuffer(bmp_str, dtype='uint8').reshape((bmp_info['bmHeight'], bmp_info['bmWidth'], 4))

        #img = img[top:bottom, left:right]
        # 测试透明通道是否截到
        # img = cv2.split(img)
        # # 提取Alpha通道
        # img = img[3]
        # cv2.imshow('Alpha Channel', img)
        # cv2.waitKey(0)
        # cv2.destroyAllWindows()
        if filename is not None:
            # 保存位图对象到文件
            img_pil = Image.fromarray(img[..., [2, 1, 0]])
            img_pil.save(filename, format='JPEG', quality=90)

        # 删除对象，释放资源
        save_dc.DeleteDC()

        win32gui.ReleaseDC(hwnd, hwnd_dc)
        win32gui.DeleteObject(save_bitmap.GetHandle())
        #print(time.time()-old_time)
        return img

    except :
        logger.error(traceback.format_exc())
        return False



if __name__ == '__main__':
    try:
        step=int(input("多少秒截一张图?输入数字按回车确认:"))
    except:
        step=2
    input("请将游戏设置为1920*1080分辨率,然后按回车开始截图")
    # 获取窗口句柄
    hwnd = win32gui.FindWindow("UnityWndClass", "原神")  # 替换成你实际的窗口句柄
    # 设定截图区域的左上角坐标 (x, y) 和右下角坐标 (x, y)
    left, top, right, bottom = 0, 0, 1920, 1080  # 替换成你实际的区域坐标
    #判断是否有这个目录 没有就创建
    if not os.path.exists("../../原神截图"):
        os.mkdir("../../原神截图")
    while True:
        time.sleep(step)
        filename = f"./原神截图/{int(time.time())}{random.randint(1000,9999)}.jpg"
        ret=screenshot(hwnd, left, top, right, bottom, filename=filename)

        print("截图成功保存在:",filename)













