# -*- coding: utf-8 -*-

# Form implementation generated from reading ui file 'formitemfuben.ui'
#
# Created by: PyQt5 UI code generator 5.15.7
#
# WARNING: Any manual changes made to this file will be lost when pyuic5 is
# run again.  Do not edit this file unless you know what you are doing.


from PyQt5 import Qt<PERSON>ore, QtGui, QtWidgets


class Ui_formitemfuben(object):
    def setupUi(self, formitemfuben):
        formitemfuben.setObjectName("formitemfuben")
        formitemfuben.resize(456, 261)
        self.gridLayout = QtWidgets.QGridLayout(formitemfuben)
        self.gridLayout.setObjectName("gridLayout")
        self.led_name = QtWidgets.QLineEdit(formitemfuben)
        self.led_name.setMinimumSize(QtCore.QSize(0, 0))
        self.led_name.setMaximumSize(QtCore.QSize(999, 9999))
        self.led_name.setStyleSheet("color: rgb(255, 255, 255);")
        self.led_name.setAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignLeft|QtCore.Qt.AlignVCenter)
        self.led_name.setObjectName("led_name")
        self.gridLayout.addWidget(self.led_name, 0, 0, 1, 4)
        self.cb_is_checked = QtWidgets.QCheckBox(formitemfuben)
        self.cb_is_checked.setMinimumSize(QtCore.QSize(0, 0))
        self.cb_is_checked.setMaximumSize(QtCore.QSize(999999, 9999999))
        self.cb_is_checked.setLayoutDirection(QtCore.Qt.RightToLeft)
        self.cb_is_checked.setStyleSheet("")
        self.cb_is_checked.setText("")
        self.cb_is_checked.setChecked(True)
        self.cb_is_checked.setObjectName("cb_is_checked")
        self.gridLayout.addWidget(self.cb_is_checked, 0, 4, 1, 1)
        self.bt_start = QtWidgets.QPushButton(formitemfuben)
        self.bt_start.setMinimumSize(QtCore.QSize(50, 50))
        self.bt_start.setMaximumSize(QtCore.QSize(999999, 999999))
        self.bt_start.setObjectName("bt_start")
        self.gridLayout.addWidget(self.bt_start, 1, 3, 1, 2)
        self.bt_chuansong = QtWidgets.QPushButton(formitemfuben)
        self.bt_chuansong.setMinimumSize(QtCore.QSize(50, 50))
        self.bt_chuansong.setMaximumSize(QtCore.QSize(999999, 999999))
        self.bt_chuansong.setObjectName("bt_chuansong")
        self.gridLayout.addWidget(self.bt_chuansong, 2, 0, 1, 1)
        self.bt_moban = QtWidgets.QPushButton(formitemfuben)
        self.bt_moban.setMinimumSize(QtCore.QSize(50, 50))
        self.bt_moban.setMaximumSize(QtCore.QSize(999999, 999999))
        self.bt_moban.setObjectName("bt_moban")
        self.gridLayout.addWidget(self.bt_moban, 2, 1, 1, 1)
        self.bt_moban_maodian = QtWidgets.QPushButton(formitemfuben)
        self.bt_moban_maodian.setMinimumSize(QtCore.QSize(50, 50))
        self.bt_moban_maodian.setMaximumSize(QtCore.QSize(999999, 999999))
        self.bt_moban_maodian.setObjectName("bt_moban_maodian")
        self.gridLayout.addWidget(self.bt_moban_maodian, 2, 2, 1, 1)
        self.bt_del = QtWidgets.QPushButton(formitemfuben)
        self.bt_del.setMinimumSize(QtCore.QSize(50, 50))
        self.bt_del.setMaximumSize(QtCore.QSize(999999, 999999))
        self.bt_del.setObjectName("bt_del")
        self.gridLayout.addWidget(self.bt_del, 2, 3, 1, 2)
        self.cb_isfanxiang = QtWidgets.QCheckBox(formitemfuben)
        self.cb_isfanxiang.setObjectName("cb_isfanxiang")
        self.gridLayout.addWidget(self.cb_isfanxiang, 1, 0, 1, 1)
        self.led_cishu = QtWidgets.QLineEdit(formitemfuben)
        self.led_cishu.setStyleSheet("color: rgb(255, 255, 255);")
        self.led_cishu.setAlignment(QtCore.Qt.AlignCenter)
        self.led_cishu.setObjectName("led_cishu")
        self.gridLayout.addWidget(self.led_cishu, 1, 2, 1, 1)
        self.label = QtWidgets.QLabel(formitemfuben)
        self.label.setAlignment(QtCore.Qt.AlignCenter)
        self.label.setObjectName("label")
        self.gridLayout.addWidget(self.label, 1, 1, 1, 1)

        self.retranslateUi(formitemfuben)
        QtCore.QMetaObject.connectSlotsByName(formitemfuben)

    def retranslateUi(self, formitemfuben):
        _translate = QtCore.QCoreApplication.translate
        formitemfuben.setWindowTitle(_translate("formitemfuben", "Form"))
        self.led_name.setText(_translate("formitemfuben", "蒙德锄地"))
        self.bt_start.setText(_translate("formitemfuben", "启动"))
        self.bt_chuansong.setText(_translate("formitemfuben", "传送\n"
"脚本"))
        self.bt_moban.setText(_translate("formitemfuben", "传送\n"
"模板"))
        self.bt_moban_maodian.setText(_translate("formitemfuben", "锚点\n"
"模板"))
        self.bt_del.setText(_translate("formitemfuben", "删除\n"
"任务"))
        self.cb_isfanxiang.setText(_translate("formitemfuben", "反\n"
"入"))
        self.led_cishu.setText(_translate("formitemfuben", "99"))
        self.label.setText(_translate("formitemfuben", "刷本\n"
"次数"))
