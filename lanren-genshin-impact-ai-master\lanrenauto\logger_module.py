import logging
import os
from logging.handlers import TimedRotatingFileHandler

# 创建全局的 logger
logger = logging.getLogger("LenRenAI")
logger.setLevel(logging.DEBUG)
# 创建一个handler，用于将日志输出到控制台
console_handler = logging.StreamHandler()
console_handler.setLevel(logging.DEBUG)

log_dir = './log'
if not os.path.exists(log_dir):
    os.makedirs(log_dir)
# 创建一个handler，用于将日志输出到文件
file_handler = TimedRotatingFileHandler('./log/lanrenai.log', when='midnight', interval=1, backupCount=7)
file_handler.setLevel(logging.DEBUG)

# 定义日志消息格式
class CustomFormatter(logging.Formatter):

    FORMATS = {
        logging.DEBUG:  '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        logging.INFO: '%(asctime)s - %(name)s - %(levelname)s - %(message)s' ,
        logging.WARNING: '%(asctime)s - %(name)s - %(levelname)s - %(message)s' ,
        logging.ERROR: '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        logging.CRITICAL:  '%(asctime)s - %(name)s - %(levelname)s - %(message)s' ,
    }

    def format(self, record):
        log_fmt = self.FORMATS.get(record.levelno)
        formatter = logging.Formatter(log_fmt)
        return formatter.format(record)

#创建一个formatter格式类
formatter = CustomFormatter()

#设置消息格式
console_handler.setFormatter(formatter)
file_handler.setFormatter(formatter)

# 将handler添加到logger
logger.addHandler(console_handler)
logger.addHandler(file_handler)