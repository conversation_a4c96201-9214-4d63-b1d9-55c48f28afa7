# -*- coding: utf-8 -*-

# Form implementation generated from reading ui file 'formm.ui'
#
# Created by: PyQt5 UI code generator 5.15.7
#
# WARNING: Any manual changes made to this file will be lost when pyuic5 is
# run again.  Do not edit this file unless you know what you are doing.


from PyQt5 import QtCore, QtGui, QtWidgets


class Ui_form_m(object):
    def setupUi(self, form_m):
        form_m.setObjectName("form_m")
        form_m.resize(252, 153)
        self.verticalLayout = QtWidgets.QVBoxLayout(form_m)
        self.verticalLayout.setObjectName("verticalLayout")
        self.label = QtWidgets.QLabel(form_m)
        font = QtGui.QFont()
        font.setFamily("微软雅黑")
        font.setPointSize(12)
        self.label.setFont(font)
        self.label.setAlignment(QtCore.Qt.AlignCenter)
        self.label.setObjectName("label")
        self.verticalLayout.addWidget(self.label)
        self.cbb_m = QtWidgets.QComboBox(form_m)
        self.cbb_m.setStyleSheet("color: rgb(255, 255, 255);\n"
"margin: 0px; \n"
"padding: 0px;")
        self.cbb_m.setObjectName("cbb_m")
        self.verticalLayout.addWidget(self.cbb_m)
        self.bt_save = QtWidgets.QPushButton(form_m)
        self.bt_save.setObjectName("bt_save")
        self.verticalLayout.addWidget(self.bt_save)

        self.retranslateUi(form_m)
        QtCore.QMetaObject.connectSlotsByName(form_m)

    def retranslateUi(self, form_m):
        _translate = QtCore.QCoreApplication.translate
        form_m.setWindowTitle(_translate("form_m", "Form"))
        self.label.setText(_translate("form_m", "点击切换后需要自己重开软件"))
        self.bt_save.setText(_translate("form_m", "切换"))
