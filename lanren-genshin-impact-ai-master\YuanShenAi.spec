# -*- mode: python ; coding: utf-8 -*-

import sys
sys.setrecursionlimit(sys.getrecursionlimit() * 5)
block_cipher = None


a = Analysis(
    ['main.py'],
    datas=[('./onnxruntime', 'onnxruntime')],
    binaries=[(r'./lanrenauto/yolov/Arial.Unicode.ttf',r'./lanrenauto/yolov/'),(r'C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v11.8/bin/cudnn_ops_infer64_8.dll',r'.'),(r'C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v11.8/bin/cudnn_cnn_infer64_8.dll',r'.')],
    hiddenimports=['seaborn','PIL.ExifTags'],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)
pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name='YuanShenAi',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=['./datas/logo.png'],
)
coll = COLLECT(
    exe,
    a.binaries,
    a.zipfiles,
    a.datas,
    strip=False,
    upx=True,
    upx_exclude=[],
    name='YuanShenAi',
)
