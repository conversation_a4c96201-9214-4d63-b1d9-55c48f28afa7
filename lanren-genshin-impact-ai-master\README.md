## 懒人原神AI入门到入土教程---python懒人智能



## 免责声明:

- 米哈游声明中提到禁止使用任何第三方工具,本工具也是属于第三方范围,虽然本工具并无入侵游戏内存破坏游戏公平性的行为,只是为了方便玩家们可以解放更多时间去体验更多更优先的内容,让游戏的新鲜感更加长久,但是老米并不一定会赞同和理解我们,因此使用本工具带来的一切后果包括但不限与被封号等自行承担,本人仅将本源码和工具作为学习和探讨AI和计算机视觉相关技术的目的分享,因此概不承担任何形式的责任,包括刑事和民事等相关责任!如有异议,请在24小时之内删除本源码和程序.如果官方认为这个库严重影响了你们运营,那么可以通知我 我会立即删库!



## 它可以做什么?

- 大世界锄地打怪 副本自动化 采集 录制回放脚本 自动伐木 自动晶蝶 等 要啥功能自己录! 注意懒人AI 只是框架 提供响应的任务能力 具体作业任务要自己录或者用云端大佬定制好了的任务
- 现在已经支持python代码任务,直接执行python代码 可以实现更多灵活的功能了





## 如果你是小白处于学习目的想要体验一下  可以进免费频道和群下载一键安装包:

也欢迎大佬们进频道来分享和定制你们的路线

```
https://link3.cc/lanrenzhineng  
```



## 如果你是大佬会pytyhon ,那么你需要这样:
https://www.bilibili.com/video/BV1Mx4y147Wi

#### 1.下载python 3.12版本以上

```
https://www.python.org/ftp/python/3.12.3/python-3.12.3-amd64.exe
```



#### 2.将源码下载下来 或者自己点那个下载按钮下载 :

```
git clone https://gitee.com/LanRenZhiNeng/lanren-genshin-impact-ai.git
```

#### 3.在源码根目录下执行一下命令 安装依赖库 如果报错 自己问问GPT 或者百度问问咋办:

```
pip install -r requirements.txt
```

#### 4.执行命令 就可以启动了  如果遇到错误,请拿错误去 自己问问GPT 或者百度问问咋办::

```
python main.py
```

#### 5.云端共享资源:

```
共享资源: https://docs.qq.com/smartsheet/DRUxieWtaUktxZkZh
(源码执行的没有云端功能,可以在这里分享或者下载共享资源)
```



# 教程合集 

## --------必看 必须全部看完 不然不保证能成功运行起来!

### ------------(使用和定制路线以及连招的教程合集 有好几个视频 都要看)

```
https://www.bilibili.com/video/BV1o1421b7Kn/
```

# 有些东西需要强调一下!!!本工具主要支持win10专业版  以及win11专业版 其它版本有可能不得劲!

1.杀毒软件必须关闭,或者拉白这个文件夹,否则很容易文件被杀尤其是 躺宝连招服务插件,容易被误杀 因为权限需要比较高并且为易语言编写,百分之百误报,杀毒软件报毒的原理我相信大家都懂 没啥技术含量 不认识和权限高的都为毒呗!

2.软件为计算机视觉算法配合模拟鼠标键盘实现的,因此不排除有封号风险,虽然目前没见过! 懂滴都懂!

3.本工具底层原理涉及 人工智能  键鼠模拟 寻路算法 计算机视觉算法 以及一些自创模块  使用了python和易语言两门编程语言来实现  相

```
https://space.bilibili.com/394281846 关技术教程在python懒人智能B站主页自行翻看 
```

