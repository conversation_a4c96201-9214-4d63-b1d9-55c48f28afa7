# -*- coding: utf-8 -*-

# Form implementation generated from reading ui file 'formitemjiaoben.ui'
#
# Created by: PyQt5 UI code generator 5.15.7
#
# WARNING: Any manual changes made to this file will be lost when pyuic5 is
# run again.  Do not edit this file unless you know what you are doing.


from PyQt5 import QtCore, QtGui, QtWidgets


class Ui_formitemjiaoben(object):
    def setupUi(self, formitemjiaoben):
        formitemjiaoben.setObjectName("formitemjiaoben")
        formitemjiaoben.resize(229, 155)
        self.gridLayout = QtWidgets.QGridLayout(formitemjiaoben)
        self.gridLayout.setObjectName("gridLayout")
        self.horizontalLayout = QtWidgets.QHBoxLayout()
        self.horizontalLayout.setObjectName("horizontalLayout")
        self.led_name = QtWidgets.QLineEdit(formitemjiaoben)
        self.led_name.setMinimumSize(QtCore.QSize(0, 0))
        self.led_name.setMaximumSize(QtCore.QSize(999, 9999))
        self.led_name.setStyleSheet("color: rgb(255, 255, 255);")
        self.led_name.setAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignLeft|QtCore.Qt.AlignVCenter)
        self.led_name.setObjectName("led_name")
        self.horizontalLayout.addWidget(self.led_name)
        self.cb_is_checked = QtWidgets.QCheckBox(formitemjiaoben)
        self.cb_is_checked.setMinimumSize(QtCore.QSize(0, 0))
        self.cb_is_checked.setMaximumSize(QtCore.QSize(999999, 9999999))
        self.cb_is_checked.setLayoutDirection(QtCore.Qt.RightToLeft)
        self.cb_is_checked.setStyleSheet("")
        self.cb_is_checked.setText("")
        self.cb_is_checked.setChecked(True)
        self.cb_is_checked.setObjectName("cb_is_checked")
        self.horizontalLayout.addWidget(self.cb_is_checked)
        self.gridLayout.addLayout(self.horizontalLayout, 0, 0, 1, 4)
        self.cb_auto_f = QtWidgets.QCheckBox(formitemjiaoben)
        self.cb_auto_f.setMinimumSize(QtCore.QSize(50, 50))
        font = QtGui.QFont()
        font.setFamily("ADMUI3Lg")
        font.setPointSize(14)
        self.cb_auto_f.setFont(font)
        self.cb_auto_f.setFocusPolicy(QtCore.Qt.TabFocus)
        self.cb_auto_f.setChecked(True)
        self.cb_auto_f.setObjectName("cb_auto_f")
        self.gridLayout.addWidget(self.cb_auto_f, 1, 0, 1, 2)
        self.bt_jiaoben = QtWidgets.QPushButton(formitemjiaoben)
        self.bt_jiaoben.setMinimumSize(QtCore.QSize(50, 50))
        self.bt_jiaoben.setMaximumSize(QtCore.QSize(999999, 999999))
        self.bt_jiaoben.setObjectName("bt_jiaoben")
        self.gridLayout.addWidget(self.bt_jiaoben, 1, 2, 1, 1)
        self.bt_start = QtWidgets.QPushButton(formitemjiaoben)
        self.bt_start.setMinimumSize(QtCore.QSize(50, 50))
        self.bt_start.setMaximumSize(QtCore.QSize(999999, 999999))
        self.bt_start.setObjectName("bt_start")
        self.gridLayout.addWidget(self.bt_start, 1, 3, 1, 1)
        self.bt_chuansong = QtWidgets.QPushButton(formitemjiaoben)
        self.bt_chuansong.setMinimumSize(QtCore.QSize(50, 50))
        self.bt_chuansong.setMaximumSize(QtCore.QSize(999999, 999999))
        self.bt_chuansong.setObjectName("bt_chuansong")
        self.gridLayout.addWidget(self.bt_chuansong, 2, 0, 1, 1)
        self.bt_moban = QtWidgets.QPushButton(formitemjiaoben)
        self.bt_moban.setMinimumSize(QtCore.QSize(50, 50))
        self.bt_moban.setMaximumSize(QtCore.QSize(999999, 999999))
        self.bt_moban.setObjectName("bt_moban")
        self.gridLayout.addWidget(self.bt_moban, 2, 1, 1, 1)
        self.bt_moban_maodian = QtWidgets.QPushButton(formitemjiaoben)
        self.bt_moban_maodian.setMinimumSize(QtCore.QSize(50, 50))
        self.bt_moban_maodian.setMaximumSize(QtCore.QSize(999999, 999999))
        self.bt_moban_maodian.setObjectName("bt_moban_maodian")
        self.gridLayout.addWidget(self.bt_moban_maodian, 2, 2, 1, 1)
        self.bt_del = QtWidgets.QPushButton(formitemjiaoben)
        self.bt_del.setMinimumSize(QtCore.QSize(50, 50))
        self.bt_del.setMaximumSize(QtCore.QSize(999999, 999999))
        self.bt_del.setObjectName("bt_del")
        self.gridLayout.addWidget(self.bt_del, 2, 3, 1, 1)

        self.retranslateUi(formitemjiaoben)
        QtCore.QMetaObject.connectSlotsByName(formitemjiaoben)

    def retranslateUi(self, formitemjiaoben):
        _translate = QtCore.QCoreApplication.translate
        formitemjiaoben.setWindowTitle(_translate("formitemjiaoben", "Form"))
        self.led_name.setText(_translate("formitemjiaoben", "蒙德锄地"))
        self.cb_auto_f.setText(_translate("formitemjiaoben", "自动按F"))
        self.bt_jiaoben.setText(_translate("formitemjiaoben", "脚本"))
        self.bt_start.setText(_translate("formitemjiaoben", "启动"))
        self.bt_chuansong.setText(_translate("formitemjiaoben", "传送\n"
"脚本"))
        self.bt_moban.setText(_translate("formitemjiaoben", "传送\n"
"模板"))
        self.bt_moban_maodian.setText(_translate("formitemjiaoben", "锚点\n"
"模板"))
        self.bt_del.setText(_translate("formitemjiaoben", "删除\n"
"任务"))
